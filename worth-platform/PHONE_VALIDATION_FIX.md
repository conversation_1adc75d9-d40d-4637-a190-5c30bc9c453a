# Phone Validation Fix for Client Creation

## 🐛 **Issue**
Client creation was failing with validation error:
```json
{
  "message": "Validation failed",
  "error": {
    "code": "BAD_REQUEST",
    "details": {
      "errors": ["phone must be a valid phone number"]
    }
  }
}
```

## 🔍 **Root Cause**
The `CreateClientDto` was using `@IsPhoneNumber()` decorator which enforces strict international phone number format validation. Local phone numbers like "0715205788" (Kenyan format) were being rejected because they don't include the country code.

## ✅ **Solution**
Updated `src/client/dto/create-client.dto.ts` to use more flexible phone validation:

### Before (Strict):
```typescript
@ApiProperty({description: 'Primary phone number'})
@IsPhoneNumber()
phone: string;

@ApiPropertyOptional({description: 'Alternative phone number'})
@IsOptional()
@IsPhoneNumber()
altPhone?: string;
```

### After (Flexible):
```typescript
@ApiProperty({description: 'Primary phone number'})
@IsString()
@IsNotEmpty()
phone: string;

@ApiPropertyOptional({description: 'Alternative phone number'})
@IsOptional()
@IsString()
altPhone?: string;
```

## 📝 **Changes Made**

1. **Updated imports** - Removed `IsPhoneNumber` from class-validator imports
2. **Changed phone validation** - Replaced `@IsPhoneNumber()` with `@IsString()` and `@IsNotEmpty()`
3. **Updated altPhone validation** - Replaced `@IsPhoneNumber()` with `@IsString()`

## 🧪 **Testing**
The fix allows phone numbers in various formats:
- ✅ Local format: "0715205788"
- ✅ International format: "+254715205788"
- ✅ Other international formats: "+1234567890"

## 🔄 **Consistency Check**
Verified that other DTOs (Tenant, Company, User) already use flexible string validation for phone numbers, so this change brings Client DTO in line with the rest of the system.

## 📋 **Test Data**
The following client creation should now work:
```json
{
  "clientType": "INDIVIDUAL",
  "firstName": "Harvey",
  "lastName": "Juma",
  "email": "<EMAIL>",
  "phone": "0715205788",
  "address": "Jogoo rd",
  "city": "Nairobi",
  "country": "Kenya",
  "postalCode": "242444",
  "nationalId": "50106508",
  "tenantIds": ["02026865-b1cb-405c-826f-5b9d90edd310"],
  "companyIds": ["e3a43d22-23ac-498b-885f-2d389d3f2e3a"]
}
```
