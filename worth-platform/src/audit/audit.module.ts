import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditService } from './audit.service';
import { AuditController } from './audit.controller';
import { AuditTrail } from './entities/audit-trail.entity';
import { User } from '../users/entities/user.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([AuditTrail, User])
    ],
    controllers: [AuditController],
    providers: [AuditService],
    exports: [AuditService]
})
export class AuditModule {}
