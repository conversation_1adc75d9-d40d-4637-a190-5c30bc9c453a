import {
    Injectable,
    NestInterceptor,
    ExecutionContext,
    CallHandler,
    Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { AuditService, AuditLogOptions } from '../audit.service';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

// Decorator to mark methods for auditing
export const AUDIT_KEY = 'audit';
export interface AuditMetadata {
    module: AuditModule;
    action: AuditAction;
    entityName?: string;
    description?: string;
}

export const Audit = (metadata: AuditMetadata) => {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
        Reflect.defineMetadata(AUDIT_KEY, metadata, descriptor.value);
        return descriptor;
    };
};

@Injectable()
export class AuditInterceptor implements NestInterceptor {
    private readonly logger = new Logger(AuditInterceptor.name);

    constructor(
        private readonly reflector: Reflector,
        private readonly auditService: AuditService,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const auditMetadata = this.reflector.get<AuditMetadata>(
            AUDIT_KEY,
            context.getHandler(),
        );

        if (!auditMetadata) {
            return next.handle();
        }

        const request = context.switchToHttp().getRequest();
        const user = request.user; // Assuming user is attached to request by auth guard
        const ipAddress = this.getClientIp(request);
        const userAgent = request.headers['user-agent'];

        return next.handle().pipe(
            tap(async (response) => {
                try {
                    await this.createAuditLog({
                        ...auditMetadata,
                        user,
                        ipAddress,
                        userAgent,
                        request,
                        response,
                        success: true,
                    });
                } catch (error) {
                    this.logger.error(`Failed to create audit log: ${error.message}`);
                }
            }),
            catchError(async (error) => {
                try {
                    await this.createAuditLog({
                        ...auditMetadata,
                        user,
                        ipAddress,
                        userAgent,
                        request,
                        response: null,
                        success: false,
                        error,
                    });
                } catch (auditError) {
                    this.logger.error(`Failed to create error audit log: ${auditError.message}`);
                }
                throw error;
            }),
        );
    }

    private async createAuditLog(options: {
        module: AuditModule;
        action: AuditAction;
        entityName?: string;
        description?: string;
        user?: any;
        ipAddress?: string;
        userAgent?: string;
        request: any;
        response: any;
        success: boolean;
        error?: any;
    }) {
        const {
            module,
            action,
            entityName,
            description,
            user,
            ipAddress,
            userAgent,
            request,
            response,
            success,
            error,
        } = options;

        // Extract entity ID from request params or response
        let entityId: string | undefined;
        if (request.params?.id) {
            entityId = request.params.id;
        } else if (response?.id) {
            entityId = response.id;
        }

        // Generate description if not provided
        let finalDescription = description;
        if (!finalDescription) {
            const actionText = this.getActionText(action);
            const entityText = entityName || this.extractEntityFromUrl(request.url);
            finalDescription = success 
                ? `${actionText} ${entityText}${entityId ? ` (ID: ${entityId})` : ''}`
                : `Failed to ${actionText.toLowerCase()} ${entityText}${error ? `: ${error.message}` : ''}`;
        }

        // Prepare audit log data
        const auditLogOptions: AuditLogOptions = {
            module,
            action,
            entityId,
            entityName: entityName || this.extractEntityFromUrl(request.url),
            description: finalDescription,
            user,
            ipAddress,
            userAgent,
            metadata: JSON.stringify({
                method: request.method,
                url: request.url,
                success,
                timestamp: new Date().toISOString(),
            }),
        };

        // Add old/new values for specific actions
        if (action === AuditAction.CREATE && response) {
            auditLogOptions.newValues = this.sanitizeData(response);
        } else if (action === AuditAction.UPDATE) {
            if (request.body) {
                auditLogOptions.newValues = this.sanitizeData(request.body);
            }
            // Note: To get old values, we'd need to fetch them before the update
            // This could be implemented by modifying the service methods
        }

        await this.auditService.log(auditLogOptions);
    }

    private getClientIp(request: any): string {
        return (
            request.headers['x-forwarded-for']?.split(',')[0] ||
            request.headers['x-real-ip'] ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request.ip ||
            'unknown'
        );
    }

    private getActionText(action: AuditAction): string {
        const actionMap = {
            [AuditAction.CREATE]: 'Created',
            [AuditAction.UPDATE]: 'Updated',
            [AuditAction.DELETE]: 'Deleted',
            [AuditAction.VIEW]: 'Viewed',
            [AuditAction.LOGIN]: 'Logged in',
            [AuditAction.LOGOUT]: 'Logged out',
            [AuditAction.ACTIVATE]: 'Activated',
            [AuditAction.DEACTIVATE]: 'Deactivated',
            [AuditAction.APPROVE]: 'Approved',
            [AuditAction.REJECT]: 'Rejected',
            [AuditAction.SCHEDULE]: 'Scheduled',
            [AuditAction.COMPLETE]: 'Completed',
            [AuditAction.CANCEL]: 'Cancelled',
            [AuditAction.EXPORT]: 'Exported',
            [AuditAction.IMPORT]: 'Imported',
        };
        return actionMap[action] || action;
    }

    private extractEntityFromUrl(url: string): string {
        // Extract entity name from URL path
        const pathSegments = url.split('/').filter(segment => segment && segment !== 'api' && segment !== 'v1');
        return pathSegments[0] || 'unknown';
    }

    private sanitizeData(data: any): Record<string, any> {
        if (!data || typeof data !== 'object') {
            return {};
        }

        const sanitized = { ...data };
        
        // Remove sensitive fields
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        });

        return sanitized;
    }
}
