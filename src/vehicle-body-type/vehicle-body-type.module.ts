import { Module } from '@nestjs/common';
import { VehicleBodyTypeService } from './vehicle-body-type.service';
import { VehicleBodyTypeController } from './vehicle-body-type.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleBodyType} from "./entities/vehicle-body-type.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleBodyType])
    ],
  controllers: [VehicleBodyTypeController],
  providers: [VehicleBodyTypeService],
    exports: [VehicleBodyTypeService]
})
export class VehicleBodyTypeModule {}
