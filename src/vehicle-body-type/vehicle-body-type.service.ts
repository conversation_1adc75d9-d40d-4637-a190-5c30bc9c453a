import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleBodyTypeDto} from './dto/create-vehicle-body-type.dto';
import {VehicleBodyTypeResponseDto} from "./dto/vehicle-body-type-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleBodyType} from "./entities/vehicle-body-type.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleBodyTypeDto} from "./dto/update-vehicle-body-type.dto";

@Injectable()
export class VehicleBodyTypeService {
    constructor(
        @InjectRepository(VehicleBodyType) private readonly vehicleBodyTypeRepository: Repository<VehicleBodyType>,
    ) {
    }

    async create(createVehicleBodyTypeDto: CreateVehicleBodyTypeDto): Promise<{
        data: VehicleBodyTypeResponseDto;
        message: string
    }> {
        const existingVehicleBodyType = await this.vehicleBodyTypeRepository.findOne({
            where: {name: createVehicleBodyTypeDto.name},
        });

        if (existingVehicleBodyType) {
            throw new ConflictException('Vehicle body type with this name already exists');
        }

        const vehicleBodyType = this.vehicleBodyTypeRepository.create({...createVehicleBodyTypeDto});
        const savedVehicleBodyType = await this.vehicleBodyTypeRepository.save(vehicleBodyType);

        return {
            data: plainToInstance(VehicleBodyTypeResponseDto, savedVehicleBodyType, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle body type successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleBodyTypeResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleBodyTypeRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleBodyTypeResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle body types retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleBodyTypeResponseDto;
        message: string;
    }> {
        let vehicleBodyType: VehicleBodyType | null = null;

        if (isUUID(query)) {
            vehicleBodyType = await this.vehicleBodyTypeRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleBodyType) {
            vehicleBodyType = await this.vehicleBodyTypeRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleBodyType) {
            throw new NotFoundException(
                `Vehicle body type with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleBodyTypeResponseDto, vehicleBodyType, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle body type retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleBodyTypeDto: UpdateVehicleBodyTypeDto,
    ): Promise<{ data: VehicleBodyTypeResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleBodyType = await this.vehicleBodyTypeRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleBodyType) {
            throw new NotFoundException('Vehicle body type with ID or name not found.');
        }

        if (
            updateVehicleBodyTypeDto.name &&
            updateVehicleBodyTypeDto.name !== vehicleBodyType.name
        ) {
            const existing = await this.vehicleBodyTypeRepository.findOne({
                where: {name: updateVehicleBodyTypeDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle body type with the provided name already exists.');
            }
        }

        Object.assign(vehicleBodyType, updateVehicleBodyTypeDto);
        const updated = await this.vehicleBodyTypeRepository.save(vehicleBodyType);

        const data = plainToInstance(VehicleBodyTypeResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle body type successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleBodyType = await this.vehicleBodyTypeRepository.findOne({where: {id}});

        if (!vehicleBodyType) {
            throw new NotFoundException(`Vehicle body type with id ${id} not found`);
        }

        await this.vehicleBodyTypeRepository.remove(vehicleBodyType);

        return {message: 'The vehicle body type successfully deleted.'};
    }
}
