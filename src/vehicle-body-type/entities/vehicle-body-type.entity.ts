import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_body_type')
export class VehicleBodyType extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle body type'})
    name: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleBodyType)
    @ApiProperty({description: 'Vehicles with this body type', type: () => [Vehicle]})
    vehicles: Vehicle[];
}
