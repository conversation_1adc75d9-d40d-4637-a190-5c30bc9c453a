import {Body, Controller, Delete, Get, Param, ParseUUI<PERSON>ipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleBodyTypeService} from './vehicle-body-type.service';
import {CreateVehicleBodyTypeDto} from './dto/create-vehicle-body-type.dto';
import {UpdateVehicleBodyTypeDto} from './dto/update-vehicle-body-type.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleBodyType} from "./entities/vehicle-body-type.entity";

@ApiTags('Vehicle Body Type')
@Controller('vehicle-body-type')
export class VehicleBodyTypeController {
    constructor(private readonly vehicleBodyTypeService: VehicleBodyTypeService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle body type'})
    @ApiResponse({status: 201, description: 'The vehicle body type has been successfully created.', type: VehicleBodyType})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    // @ApiResponse({status: 404, description: 'Vehicle body type not found.'})
    @ApiResponse({status: 409, description: 'Vehicle body type already exists.'})
    create(@Body() createVehicleBodyTypeDto: CreateVehicleBodyTypeDto) {
        return this.vehicleBodyTypeService.create(createVehicleBodyTypeDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle BodyType'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle body type retrieved successfully.',
        type: VehicleBodyType,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle body type with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleBodyTypeService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle body type by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle body type ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle body type retrieved successfully.',
        type: VehicleBodyType,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle body type with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleBodyTypeService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle body type'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle body type ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle body type successfully updated.',
        type: VehicleBodyType
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle body type with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle body type with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleBodyTypeDto: UpdateVehicleBodyTypeDto
    ) {
        return this.vehicleBodyTypeService.update(identifier, updateVehicleBodyTypeDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle body type'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle body type ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle body type successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle body type with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleBodyTypeService.remove(id);
    }

}
