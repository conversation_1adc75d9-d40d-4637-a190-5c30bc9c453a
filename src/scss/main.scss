@import 'tailwind';
@import 'vuestic';
@import 'icon-fonts/index';

body {
  @apply text-regularMedium;

  // TODO Move to some other place so that it's more elegant.
  --va-checkbox-font-size: 0.875rem;
  --va-card-box-shadow: none; // TODO Remove after https://github.com/epicmaxco/vuestic-ui/issues/3964
  --va-card-padding: 1rem;
  --va-font-family: 'Inter', sans-serif;
}

code,
kbd,
samp,
pre {
  font-family: monospace;
  font-size: 1em;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-title {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h1 {
  @apply text-[32px] md:text-5xl font-bold leading-9 md:leading-[56px] max-sm:mt-6 mb-6 md:mb-4;
}

.h3,
.page-sub-title {
  @apply text-2xl font-bold leading-[30px];
}

.h5 {
  @apply font-bold leading-tight;
}

.block-title {
  @apply text-2xl font-bold mb-2;
}

.pricing-plan-card-title {
  @apply text-[28px] md:text-[32px] leading-10 font-bold;
}

.text-regular-small {
  font-size: 0.8125rem;
  line-height: 1rem;
}

/* Fix for CSS warning: align-items: start should be flex-start */
.number-option {
  align-items: flex-start !important;
}

/* Global fix for any align-items: start usage */
[style*="align-items: start"] {
  align-items: flex-start !important;
}
