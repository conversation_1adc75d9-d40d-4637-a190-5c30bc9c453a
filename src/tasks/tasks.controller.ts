import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Task } from './entities/task.entity';

@ApiTags('tasks')
@Controller('tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new task' })
  @ApiResponse({ 
    status: 201, 
    description: 'The task has been successfully created.', 
    type: Task,
    examples: {
      'success': {
        summary: 'Successfully created task',
        value: {
          id: 1,
          title: 'Complete project documentation',
          description: 'Write comprehensive documentation for the API endpoints',
          status: 'PENDING',
          priority: 'MEDIUM',
          dueDate: '2024-04-01T00:00:00.000Z',
          assignedTo: 1,
          createdAt: '2024-03-26T10:00:00.000Z',
          updatedAt: '2024-03-26T10:00:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createTaskDto: CreateTaskDto) {
    return this.tasksService.create(createTaskDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tasks' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return all tasks.', 
    type: [Task],
    examples: {
      'success': {
        summary: 'List of tasks',
        value: [
          {
            id: 1,
            title: 'Complete project documentation',
            description: 'Write comprehensive documentation for the API endpoints',
            status: 'PENDING',
            priority: 'MEDIUM',
            dueDate: '2024-04-01T00:00:00.000Z',
            assignedTo: 1,
            createdAt: '2024-03-26T10:00:00.000Z',
            updatedAt: '2024-03-26T10:00:00.000Z'
          }
        ]
      }
    }
  })
  findAll() {
    return this.tasksService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a task by id' })
  @ApiParam({ name: 'id', description: 'Task ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return the task.', 
    type: Task,
    examples: {
      'success': {
        summary: 'Successfully retrieved task',
        value: {
          id: 1,
          title: 'Complete project documentation',
          description: 'Write comprehensive documentation for the API endpoints',
          status: 'PENDING',
          priority: 'MEDIUM',
          dueDate: '2024-04-01T00:00:00.000Z',
          assignedTo: 1,
          createdAt: '2024-03-26T10:00:00.000Z',
          updatedAt: '2024-03-26T10:00:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Task not found.',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Task not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  findOne(@Param('id') id: string) {
    return this.tasksService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a task' })
  @ApiParam({ name: 'id', description: 'Task ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'The task has been successfully updated.', 
    type: Task,
    examples: {
      'success': {
        summary: 'Successfully updated task',
        value: {
          id: 1,
          title: 'Complete project documentation',
          description: 'Write comprehensive documentation for the API endpoints',
          status: 'IN_PROGRESS',
          priority: 'HIGH',
          dueDate: '2024-04-01T00:00:00.000Z',
          assignedTo: 1,
          createdAt: '2024-03-26T10:00:00.000Z',
          updatedAt: '2024-03-26T11:00:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Task not found.',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Task not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
    return this.tasksService.update(+id, updateTaskDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a task' })
  @ApiParam({ name: 'id', description: 'Task ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'The task has been successfully deleted.',
    schema: {
      properties: {
        message: { type: 'string', example: 'Task deleted successfully' }
      }
    }
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Task not found.',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Task not found' },
        error: { type: 'string', example: 'Not Found' }
      }
    }
  })
  remove(@Param('id') id: string) {
    return this.tasksService.remove(+id);
  }
}
