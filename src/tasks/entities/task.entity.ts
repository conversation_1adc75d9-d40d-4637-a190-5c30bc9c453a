import { ApiProperty } from '@nestjs/swagger';

export class Task {
  @ApiProperty({ description: 'The unique identifier of the task' })
  id: number;

  @ApiProperty({ description: 'The title of the task' })
  title: string;

  @ApiProperty({ description: 'The description of the task' })
  description: string;

  @ApiProperty({ description: 'The status of the task', enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED'] })
  status: string;

  @ApiProperty({ description: 'The priority of the task', enum: ['LOW', 'MEDIUM', 'HIGH'] })
  priority: string;

  @ApiProperty({ description: 'The due date of the task' })
  dueDate: Date;

  @ApiProperty({ description: 'The ID of the user assigned to the task' })
  assignedTo: number;

  @ApiProperty({ description: 'The date when the task was created' })
  createdAt: Date;

  @ApiProperty({ description: 'The date when the task was last updated' })
  updatedAt: Date;
}
