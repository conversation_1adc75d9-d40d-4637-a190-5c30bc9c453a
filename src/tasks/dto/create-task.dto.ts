import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsDate, IsN<PERSON>ber, MinLength } from 'class-validator';

export class CreateTaskDto {
  @ApiProperty({ description: 'The title of the task', example: 'Complete project documentation' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  title: string;

  @ApiProperty({ description: 'The description of the task', example: 'Write comprehensive documentation for the API endpoints' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ 
    description: 'The status of the task', 
    enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED'],
    example: 'PENDING'
  })
  @IsEnum(['PENDING', 'IN_PROGRESS', 'COMPLETED'])
  @IsNotEmpty()
  status: string;

  @ApiProperty({ 
    description: 'The priority of the task', 
    enum: ['LOW', 'MEDIUM', 'HIGH'],
    example: 'MEDIUM'
  })
  @IsEnum(['LOW', 'MEDIUM', 'HIGH'])
  @IsNotEmpty()
  priority: string;

  @ApiProperty({ description: 'The due date of the task', example: '2024-04-01' })
  @IsDate()
  @IsNotEmpty()
  dueDate: Date;

  @ApiProperty({ description: 'The ID of the user assigned to the task', example: 1 })
  @IsNumber()
  @IsNotEmpty()
  assignedTo: number;
}
