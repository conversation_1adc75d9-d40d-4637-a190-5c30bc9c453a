import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import * as compression from 'compression';
import { ConfigService } from '@nestjs/config';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';


async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Get config service
  const configService = app.get(ConfigService);
  
  // Enable global exception filter
  app.useGlobalFilters(new HttpExceptionFilter());

  // Enable validation pipes
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
  }));

  // Set global prefix for all routes
  app.setGlobalPrefix('api/v1');

  // Enable CORS
  app.enableCors({
    origin: [configService.get('CORS_ORIGINS')],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    exposedHeaders: ['Content-Range', 'X-Content-Range'],
    credentials: configService.get('CORS_CREDENTIALS') === 'true',
    // maxAge: parseInt(configService.get('CORS_MAX_AGE') || '3600'),
  });

  // Security middleware
  app.use(helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'http://localhost:5173', 'http://localhost:8000', 'https://api.worthplatform.com'],
        fontSrc: ["'self'", 'data:', 'https:'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
        sandbox: ['allow-forms', 'allow-scripts', 'allow-same-origin'],
      },
    },
  }));

  // Enable compression
  app.use(compression());

  // Enable rate limiting
  // app.useGlobalGuards(app.get(ThrottlerGuard));

  const config = new DocumentBuilder()
    .setTitle('Worth Platform API')
    .setDescription(`
      The Worth Platform API provides endpoints for managing vehicles, tasks, valuations, and users.
      
      ## Authentication
      All endpoints require authentication using JWT tokens. Include the token in the Authorization header:
      \`Authorization: Bearer your-token-here\`
      
      ## Rate Limiting
      API requests are limited to 100 requests per minute per IP address.
      When rate limit is exceeded, the API will return a 429 Too Many Requests response.
      Rate limiting headers are included in the response:
      - X-RateLimit-Limit: Maximum number of requests per time window
      - X-RateLimit-Remaining: Number of requests remaining in the current time window
      - X-RateLimit-Reset: Time when the rate limit will reset
      
      ## Error Responses
      The API uses standard HTTP status codes:
      - 200: Success
      - 201: Created
      - 400: Bad Request
      - 401: Unauthorized
      - 403: Forbidden
      - 404: Not Found
      - 429: Too Many Requests
      - 500: Internal Server Error

      ## CORS
      The API supports CORS for client applications. The following origins are allowed:
      - Development: http://localhost:8000
      - Production: https://worthplatform.com
    `)
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'access-token',
    )
    .addTag('Tenant', 'Tenant management endpoints')
    .addTag('Company', 'Company management endpoints')
    .addTag('Client', 'Client management endpoints')
    .addTag('Vehicle Type', 'Vehicle type management endpoints')
    .addTag('Vehicle Body Type', 'Vehicle Body type management endpoints')
    .addTag('Vehicle Make', 'Vehicle make management endpoints')
    .addTag('Vehicle Model', 'Vehicle model management endpoints')
    .addTag('Vehicle Fuel Type', 'Vehicle fuel type management endpoints')
    .addTag('Vehicle Transmission', 'Vehicle transmission management endpoints')
    .addTag('Vehicle Lighting', 'Vehicle Lighting management endpoints')
    .addTag('Vehicles', 'Vehicle management endpoints')
    .addTag('tasks', 'Task management endpoints')
    .addTag('valuations', 'Vehicle valuation endpoints')
    .addTag('users', 'User management endpoints')
    .addServer('http://localhost:8000', 'Local Development')
    .addServer('https://api.worthplatform.com', 'Production')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      filter: true,
      showRequestDuration: true,
      syntaxHighlight: {
        theme: 'monokai',
      },
    },
    customSiteTitle: 'Worth Platform API Documentation',
    customCss: '.swagger-ui .topbar { display: none }',
  });

  const port = process.env.PORT ?? 8000;
  await app.listen(port);
  
  // Log server information
  console.log('\n🚀 Worth Platform API is running!');
  console.log(`📡 Server URL: http://localhost:${port}/api/v1`);
  console.log(`📚 Swagger Documentation: http://localhost:${port}/api`);
  console.log(`🔒 CORS Enabled for: ${configService.get('CORS_ORIGINS')}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⚡ Rate Limiting: 100 requests per minute\n`);
}
bootstrap();
