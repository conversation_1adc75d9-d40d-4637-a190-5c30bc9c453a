import {defineStore} from 'pinia'
import {addCompany, getCompanies, Pagination, removeCompany, Sorting, updateCompany} from '../data/pages/companies'
import {Company} from '../pages/companies/types'

export const useCompaniesStore = defineStore('companies', {
    state: () => {
        return {
            items: [] as Company[],
            pagination: {
                page: 1,
                perPage: 10,
                total: 0,
            } as Pagination,
        }
    },

    actions: {
        async getAll(options: { pagination: Pagination; sorting?: Sorting }) {
            const {data, pagination} = await getCompanies({
                ...options.sorting,
                ...options.pagination,
            })
            this.items = data
            this.pagination = pagination
        },

        async add(company: Omit<Company, 'id' | 'createdAt' | 'updatedAt'>) {
            const newCompany = await addCompany(company)
            this.items.push(newCompany)
        },

        async update(company: Company) {
            const updatedCompany = await updateCompany(company)
            const index = this.items.findIndex(({id}) => id === company.id)
            this.items.splice(index, 1, updatedCompany)
        },

        async remove(company: Company) {
            const isRemoved = await removeCompany(company)

            if (isRemoved) {
                const index = this.items.findIndex(({id}) => id === company.id)
                this.items.splice(index, 1)
            }
        },
    },
})
