import {defineStore} from 'pinia'
import {addClient, getClients, Pagination, removeClient, Sorting, updateClient} from '../data/pages/clients'
import {Client} from '../pages/clients/types'

export const useClientsStore = defineStore('clients', {
    state: () => {
        return {
            items: [] as Client[],
            pagination: {
                page: 1,
                perPage: 10,
                total: 0,
            } as Pagination,
        }
    },

    actions: {
        async getAll(options: { pagination: Pagination; sorting?: Sorting }) {
            const {data, pagination} = await getClients({
                ...options.sorting,
                ...options.pagination,
            })
            this.items = data
            this.pagination = pagination
        },

        async add(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) {
            const newClient = await addClient(client)
            this.items.push(newClient)
        },

        async update(client: Client) {
            const updatedClient = await updateClient(client)
            const index = this.items.findIndex(({id}) => id === client.id)
            this.items.splice(index, 1, updatedClient)
        },

        async remove(client: Client) {
            const isRemoved = await removeClient(client)

            if (isRemoved) {
                const index = this.items.findIndex(({id}) => id === client.id)
                this.items.splice(index, 1)
            }
        },
    },
})
