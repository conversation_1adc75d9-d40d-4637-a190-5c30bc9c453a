import {defineStore} from 'pinia'
import {addVehicleType, getVehicleTypes, Pagination, removeVehicleType, Sorting, updateVehicleType} from '../data/pages/vehicleTypes'
import {VehicleType} from '../pages/vehicle-types/types'

export const useVehicleTypesStore = defineStore('vehicleTypes', {
    state: () => {
        return {
            items: [] as VehicleType[],
            pagination: {
                page: 1,
                perPage: 10,
                total: 0,
            } as Pagination,
        }
    },

    actions: {
        async getAll(options: { pagination: Pagination; sorting?: Sorting }) {
            const {data, pagination} = await getVehicleTypes({
                ...options.sorting,
                ...options.pagination,
            })
            this.items = data
            this.pagination = pagination
        },

        async add(vehicleType: Omit<VehicleType, 'id' | 'createdAt'>) {
            const newVehicleType = await addVehicleType(vehicleType)
            this.items.push(newVehicleType)
        },

        async update(vehicleType: VehicleType) {
            console.log("Store Data received",vehicleType)
            const updatedVehicleType = await updateVehicleType(vehicleType)
            const index = this.items.findIndex(({id}) => id === vehicleType.id)
            this.items.splice(index, 1, updatedVehicleType)
        },

        async remove(vehicleType: VehicleType) {
            const isRemoved = await removeVehicleType(vehicleType)

            if (isRemoved) {
                const index = this.items.findIndex(({id}) => id === vehicleType.id)
                this.items.splice(index, 1)
            }
        },
    },
})
