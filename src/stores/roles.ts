import { defineStore } from 'pinia';
import {
    addRole,
    type Filters,
    getRoles,
    Pagination,
    removeRole,
    Sorting,
    updateRole,
    uploadAvatar,
} from '../data/pages/roles';
import { Role } from '../pages/roles/types';

export const useRolesStore = defineStore('roles', {
    state: () => {
        return {
            items: [] as Role[],
            pagination: { page: 1, perPage: 10, total: 0 },
        };
    },

    actions: {
        async getAll(options: { pagination?: Pagination; sorting?: Sorting; filters?: Partial<Filters> }) {
            const { data, pagination } = await getRoles({
                ...options.filters,
                ...options.sorting,
                ...options.pagination,
            });
            this.items = data;
            this.pagination = pagination;
        },

        async add(role: Role) {
            const [newRole] = await addRole(role);
            this.items.unshift(newRole);
            return newRole;
        },

        async update(role: Role) {
            const [updatedRole] = await updateRole(role);
            const index = this.items.findIndex(({ id }) => id === role.id);
            this.items.splice(index, 1, updatedRole);
            return updatedRole;
        },

        async remove(role: Role) {
            const isRemoved = await removeRole(role);

            if (isRemoved) {
                const index = this.items.findIndex(({ id }) => id === role.id);
                this.items.splice(index, 1);
            }
        },

        async uploadAvatar(formData: FormData) {
            return uploadAvatar(formData);
        },
    },
});
