import { defineStore } from 'pinia';
import { authService } from '../services/auth.service';

interface User {
    id: string;
    email: string;
    name: string;
}

export const useUserStore = defineStore('user', {
    state: () => {
        return {
            user: null as User | null,
            isAuthenticated: false,
            loading: false,
            error: null as string | null,
        };
    },

    actions: {
        async login(email: string, password: string) {
            try {
                this.loading = true;
                this.error = null;
                const response = await authService.login({ email, password });
                this.user = response.user;
                this.isAuthenticated = true;
            } catch (error: any) {
                this.error = error.response?.data?.message || 'Login failed';
                throw error;
            } finally {
                this.loading = false;
            }
        },

        logout() {
            this.user = null;
            this.isAuthenticated = false;
            authService.removeAuthToken();
        },

        checkAuth() {
            const token = authService.getAuthToken();
            this.isAuthenticated = !!token;
        },
    },
});
