# Color Scheme Documentation

## Primary Color Palette

| Role           | Color Code | Color Name | Usage |
| -------------- | ---------- | ---------- | ----- |
| Primary        | `#009688`  | Teal       | Main brand color, primary buttons, links |
| Secondary      | `#3949AB`  | Indigo     | Secondary buttons, accents |
| Accent         | `#43A047`  | Green      | Success states, positive actions |

## Background Colors

| Role                    | Light Theme | Dark Theme | Usage |
| ----------------------- | ----------- | ---------- | ----- |
| Background Primary      | `#F5F5F5`   | `#121212`  | Main page background |
| Background Secondary    | `#FFFFFF`   | `#1E1E1E`  | Cards, modals, overlays |
| Background Element      | `#FAFAFA`   | `#2C2C2C`  | Input fields, buttons |
| Background Card Primary | `#FFFFFF`   | `#1E1E1E`  | Primary cards |
| Background Card Secondary | `#F9F9F9` | `#2C2C2C`  | Secondary cards |
| Background Border       | `#E0E0E0`   | `#404040`  | Borders, dividers |

## Text Colors

| Role           | Light Theme | Dark Theme | Usage |
| -------------- | ----------- | ---------- | ----- |
| Text Primary   | `#212121`   | `#FFFFFF`  | Main text content |
| Text Secondary | `#757575`   | `#B0B0B0`  | Secondary text, labels |
| Text Inverted  | `#FFFFFF`   | `#212121`  | Text on colored backgrounds |

## Status Colors

| Role    | Color Code | Usage |
| ------- | ---------- | ----- |
| Success | `#43A047`  | Success messages, positive states |
| Info    | `#2196F3`  | Information messages, neutral states |
| Warning | `#FF9800`  | Warning messages, caution states |
| Danger  | `#F44336`  | Error messages, destructive actions |

## Badge Colors (for Roles & Permissions)

The following colors are used for role and permission badges:

1. `#009688` - Primary Teal
2. `#3949AB` - Secondary Indigo
3. `#43A047` - Success Green
4. `#2196F3` - Info Blue
5. `#FF9800` - Warning Orange
6. `#9C27B0` - Purple
7. `#E91E63` - Pink
8. `#795548` - Brown

## CSS Variables

These colors are available as CSS variables throughout the application:

```css
/* Primary Colors */
--va-primary: #009688
--va-secondary: #3949AB

/* Background Colors */
--va-background-primary: #F5F5F5 (light) / #121212 (dark)
--va-background-secondary: #FFFFFF (light) / #1E1E1E (dark)
--va-background-element: #FAFAFA (light) / #2C2C2C (dark)

/* Text Colors */
--va-text-primary: #212121 (light) / #FFFFFF (dark)
--va-text-secondary: #757575 (light) / #B0B0B0 (dark)

/* Status Colors */
--va-success: #43A047
--va-info: #2196F3
--va-danger: #F44336
--va-warning: #FF9800
```

## Usage Examples

### In Vue Components
```vue
<VaButton color="primary">Primary Button</VaButton>
<VaButton color="secondary">Secondary Button</VaButton>
<VaBadge color="#009688" text="Teal Badge" />
```

### In CSS/SCSS
```scss
.custom-element {
  background-color: var(--va-primary);
  color: var(--va-text-inverted);
}
```

### In Tailwind Classes
```html
<div class="bg-primary text-textInverted">
  Primary colored element
</div>
```

## Theme Switching

The application supports both light and dark themes. Colors automatically adjust based on the selected theme while maintaining the same primary color palette.
