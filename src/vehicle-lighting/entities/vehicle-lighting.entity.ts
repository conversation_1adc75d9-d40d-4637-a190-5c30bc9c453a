import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_lighting')
export class VehicleLighting extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle lighting'})
    name: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleLighting)
    @ApiProperty({description: 'Vehicles with this lighting type', type: () => [Vehicle]})
    vehicles: Vehicle[];
}