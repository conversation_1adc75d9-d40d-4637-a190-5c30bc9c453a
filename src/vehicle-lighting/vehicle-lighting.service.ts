import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleLightingDto} from './dto/create-vehicle-lighting.dto';
import {VehicleLightingResponseDto} from "./dto/vehicle-lighting-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleLighting} from "./entities/vehicle-lighting.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleLightingDto} from "./dto/update-vehicle-lighting.dto";

@Injectable()
export class VehicleLightingService {
    constructor(
        @InjectRepository(VehicleLighting) private readonly vehicleLightingRepository: Repository<VehicleLighting>,
    ) {
    }

    async create(createVehicleLightingDto: CreateVehicleLightingDto): Promise<{
        data: VehicleLightingResponseDto;
        message: string
    }> {
        const existingVehicleLighting = await this.vehicleLightingRepository.findOne({
            where: {name: createVehicleLightingDto.name},
        });

        if (existingVehicleLighting) {
            throw new ConflictException('Vehicle lighting with this name already exists');
        }

        const vehicleLighting = this.vehicleLightingRepository.create({...createVehicleLightingDto});
        const savedVehicleLighting = await this.vehicleLightingRepository.save(vehicleLighting);

        return {
            data: plainToInstance(VehicleLightingResponseDto, savedVehicleLighting, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle lighting successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleLightingResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleLightingRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleLightingResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle lightings retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleLightingResponseDto;
        message: string;
    }> {
        let vehicleLighting: VehicleLighting | null = null;

        if (isUUID(query)) {
            vehicleLighting = await this.vehicleLightingRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleLighting) {
            vehicleLighting = await this.vehicleLightingRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleLighting) {
            throw new NotFoundException(
                `Vehicle lighting with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleLightingResponseDto, vehicleLighting, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle lighting retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleLightingDto: UpdateVehicleLightingDto,
    ): Promise<{ data: VehicleLightingResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleLighting = await this.vehicleLightingRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleLighting) {
            throw new NotFoundException('Vehicle lighting with ID or name not found.');
        }

        if (
            updateVehicleLightingDto.name &&
            updateVehicleLightingDto.name !== vehicleLighting.name
        ) {
            const existing = await this.vehicleLightingRepository.findOne({
                where: {name: updateVehicleLightingDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle lighting with the provided name already exists.');
            }
        }

        Object.assign(vehicleLighting, updateVehicleLightingDto);
        const updated = await this.vehicleLightingRepository.save(vehicleLighting);

        const data = plainToInstance(VehicleLightingResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle lighting successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleLighting = await this.vehicleLightingRepository.findOne({where: {id}});

        if (!vehicleLighting) {
            throw new NotFoundException(`Vehicle lighting with id ${id} not found`);
        }

        await this.vehicleLightingRepository.remove(vehicleLighting);

        return {message: 'The vehicle lighting successfully deleted.'};
    }
}
