import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleLightingService} from './vehicle-lighting.service';
import {CreateVehicleLightingDto} from './dto/create-vehicle-lighting.dto';
import {UpdateVehicleLightingDto} from './dto/update-vehicle-lighting.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleLighting} from "./entities/vehicle-lighting.entity";

@ApiTags('Vehicle Lighting')
@Controller('vehicle-lighting')
export class VehicleLightingController {
    constructor(private readonly vehicleLightingService: VehicleLightingService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle lighting'})
    @ApiResponse({status: 201, description: 'The vehicle lighting has been successfully created.', type: VehicleLighting})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    // @ApiResponse({status: 404, description: 'Vehicle lighting not found.'})
    @ApiResponse({status: 409, description: 'Vehicle lighting already exists.'})
    create(@Body() createVehicleLightingDto: CreateVehicleLightingDto) {
        return this.vehicleLightingService.create(createVehicleLightingDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Lighting'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle lighting retrieved successfully.',
        type: VehicleLighting,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle lighting with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleLightingService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle lighting by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle lighting ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle lighting retrieved successfully.',
        type: VehicleLighting,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle lighting with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleLightingService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle lighting'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle lighting ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle lighting successfully updated.',
        type: VehicleLighting
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle lighting with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle lighting with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleLightingDto: UpdateVehicleLightingDto
    ) {
        return this.vehicleLightingService.update(identifier, updateVehicleLightingDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle lighting'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle lighting ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle lighting successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle lighting with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleLightingService.remove(id);
    }

}
