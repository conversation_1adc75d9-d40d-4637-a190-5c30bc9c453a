import { Module } from '@nestjs/common';
import { VehicleLightingService } from './vehicle-lighting.service';
import { VehicleLightingController } from './vehicle-lighting.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleLighting} from "./entities/vehicle-lighting.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleLighting])
    ],
  controllers: [VehicleLightingController],
  providers: [VehicleLightingService],
  exports:[VehicleLightingService]
})
export class VehicleLightingModule {}
