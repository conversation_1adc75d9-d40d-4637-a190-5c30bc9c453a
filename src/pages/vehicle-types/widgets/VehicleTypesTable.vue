<script setup lang="ts">
import {computed, PropType} from 'vue'
import {defineVaDataTableColumns, VaButton, VaPagination, VaSelect} from 'vuestic-ui'
import {VehicleType} from '../types'
import {Pagination, Sorting} from '../../../data/pages/vehicleTypes'
import {useVModel} from '@vueuse/core'

const columns = defineVaDataTableColumns([
    {label: 'Name', key: 'name', sortable: true},
    {label: 'Update Date', key: 'updatedAt', sortable: true},
    {label: 'Creation Date', key: 'createdAt', sortable: true},
    {label: ' ', key: 'actions'},
])

const props = defineProps({
    vehicleTypes: {
        type: Array as PropType<VehicleType[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
    sortBy: {
        type: String as PropType<Sorting['sortBy']>,
        default: undefined,
    },
    sortingOrder: {
        type: String as PropType<Sorting['sortingOrder']>,
        default: undefined,
    },
    pagination: {
        type: Object as PropType<Pagination>,
        required: true,
    },
})

const emit = defineEmits<{
    (event: 'edit', vehicleType: VehicleType): void
    (event: 'delete', vehicleType: VehicleType): void
}>()

const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))

function formatDateTime(dateStr: string): string {
  const date = new Date(dateStr)
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
}
</script>

<template>
    <div>
        <VaDataTable
            v-model:sort-by="sortByVModel"
            v-model:sorting-order="sortingOrderVModel"
            :items="vehicleTypes"
            :columns="columns"
            :loading="loading"
        >
            <template #cell(name)="{ rowData }">
                <div class="ellipsis max-w-[230px] lg:max-w-[450px]">
                    {{ rowData.name }}
                </div>
            </template>

            <template #cell(updatedAt)="{ rowData: vehicleType }">
                {{ formatDateTime(vehicleType.updatedAt) }}
            </template>

            <template #cell(createdAt)="{ rowData: vehicleType }">
                {{ formatDateTime(vehicleType.createdAt) }}
            </template>

            <template #cell(actions)="{ rowData: vehicleType }">
                <div class="flex gap-2 justify-end">
                    <VaButton
                        preset="primary"
                        size="small"
                        color="primary"
                        icon="mso-edit"
                        aria-label="Edit vehicle type"
                        @click="$emit('edit', vehicleType as VehicleType)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        icon="mso-delete"
                        color="danger"
                        aria-label="Delete vehicle type"
                        @click="$emit('delete', vehicleType as VehicleType)"
                    />
                </div>
            </template>
        </VaDataTable>
        <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
            <div>
                <b>{{ $props.pagination.total }} results.</b>
                Results per page
                <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]"/>
            </div>

            <div v-if="totalPages > 1" class="flex">
                <VaButton
                    preset="secondary"
                    icon="va-arrow-left"
                    aria-label="Previous page"
                    :disabled="$props.pagination.page === 1"
                    @click="$props.pagination.page--"
                />
                <VaButton
                    class="mr-2"
                    preset="secondary"
                    icon="va-arrow-right"
                    aria-label="Next page"
                    :disabled="$props.pagination.page === totalPages"
                    @click="$props.pagination.page++"
                />
                <VaPagination
                    v-model="$props.pagination.page"
                    buttons-preset="secondary"
                    :pages="totalPages"
                    :visible-pages="5"
                    :boundary-links="false"
                    :direction-links="false"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(tbody .va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>
