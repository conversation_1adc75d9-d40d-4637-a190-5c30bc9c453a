<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyTenant, Tenant} from '../types'
import {VaButton, VaForm, VaInput, VaSwitch} from "vuestic-ui"
import {formatDateForInput, formatDateFromInput} from '../../../services/utils'

const props = defineProps<{
    tenant: Tenant | null
    saveButtonLabel: string
    readonly?: boolean
}>()

const emit = defineEmits<{
    (event: 'save', tenant: Tenant): void
    (event: 'close'): void
}>()

const defaultNewTenant: EmptyTenant = {
    name: '',
    code: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    logoUrl: '',
    isActive: true,
    subscriptionPlan: '',
    subscriptionStartDate: '',
    subscriptionEndDate: '',
}

const newTenant = ref<Tenant | EmptyTenant>({...defaultNewTenant})

const isFormHasUnsavedChanges = computed(() => {
    if (!props.tenant) {
        // Creating new - check if any field is different from empty
        return Object.keys(defaultNewTenant).some(key =>
            newTenant.value[key as keyof EmptyTenant] !== defaultNewTenant[key as keyof EmptyTenant]
        )
    }
    // Editing existing - check if any field is different from original
    return Object.keys(newTenant.value).some(key =>
        newTenant.value[key as keyof Tenant] !== props.tenant![key as keyof Tenant]
    )
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.tenant,
    () => {
        if (!props.tenant) {
            newTenant.value = {...defaultNewTenant}
            return
        }

        newTenant.value = {
            ...props.tenant,
            // Convert ISO dates to YYYY-MM-DD format for HTML date inputs
            subscriptionStartDate: formatDateForInput(props.tenant.subscriptionStartDate || ''),
            subscriptionEndDate: formatDateForInput(props.tenant.subscriptionEndDate || ''),
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'

const handleSave = () => {
    // Convert dates back to ISO format for API
    const formData = {
        ...newTenant.value,
        subscriptionStartDate: newTenant.value.subscriptionStartDate
            ? formatDateFromInput(newTenant.value.subscriptionStartDate)
            : '',
        subscriptionEndDate: newTenant.value.subscriptionEndDate
            ? formatDateFromInput(newTenant.value.subscriptionEndDate)
            : '',
    }

    let tenantToSave
    if (props.tenant) {
        // Editing: preserve all fields, update with form values
        tenantToSave = {...props.tenant, ...formData}
    } else {
        // Creating: use form values but exclude isActive (status) as it should be auto-generated
        const {isActive, ...dataWithoutStatus} = formData
        tenantToSave = dataWithoutStatus
    }

    emit('save', tenantToSave as Tenant)
}
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newTenant.name" label="Tenant Name" :rules="[required]" :readonly="props.readonly"/>
        <!-- Code field only shown in view mode since it's auto-generated -->
        <VaInput v-if="props.readonly && props.tenant" v-model="newTenant.code" label="Tenant Code" readonly/>
        <VaInput v-model="newTenant.email" label="Email" type="email" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.phone" label="Phone" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.address" label="Address" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.website" label="Website (Optional)" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.logoUrl" label="Logo URL (Optional)" :readonly="props.readonly"/>
        <!-- Status field only shown when editing existing tenant -->
        <VaSwitch v-if="props.tenant" v-model="newTenant.isActive" label="Active Status" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.subscriptionPlan" label="Subscription Plan (Optional)" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.subscriptionStartDate" label="Subscription Start Date (Optional)" type="date" :readonly="props.readonly"/>
        <VaInput v-model="newTenant.subscriptionEndDate" label="Subscription End Date (Optional)" type="date" :readonly="props.readonly"/>
        <div v-if="!props.readonly" class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
        <div v-else class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Close</VaButton>
        </div>
    </VaForm>
</template>
