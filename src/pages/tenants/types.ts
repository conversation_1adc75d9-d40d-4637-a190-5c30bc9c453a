
export type UUID = `${string}-${string}-${string}-${string}-${string}`

export type Tenant = {
  id: UUID
  name: string
  code: string
  email: string
  phone: string
  address: string
  website?: string
  logoUrl?: string
  isActive: boolean
  subscriptionPlan?: string
  subscriptionStartDate?: string
  subscriptionEndDate?: string
  updatedAt: string
  createdAt: string
}

export type EmptyTenant = Omit<Tenant, 'id' | 'updatedAt' | 'createdAt'> & {
  name: string
  code: string
  email: string
  phone: string
  address: string
  website?: string
  logoUrl?: string
  isActive?: boolean
  subscriptionPlan?: string
  subscriptionStartDate?: string
  subscriptionEndDate?: string
}
