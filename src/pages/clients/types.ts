
export type UUID = `${string}-${string}-${string}-${string}-${string}`

export enum ClientType {
  INDIVIDUAL = 'INDIVIDUAL',
  COMPANY = 'COMPANY',
}

export type Tenant = {
  id: UUID
  name: string
  email: string
}

export type Company = {
  id: UUID
  name: string
  email: string
}

export type Client = {
  id: UUID
  clientType: ClientType
  companyName?: string
  firstName?: string
  middleName?: string
  lastName?: string
  email: string
  phone: string
  altPhone?: string
  address: string
  city: string
  country: string
  postalCode: string
  nationalId?: string
  companyRegistrationNo?: string
  taxIdNumber?: string
  contactPersonName?: string
  contactPersonDesignation?: string
  tenants?: Tenant[]
  companies?: Company[]
  updatedAt: string
  createdAt: string
}

export type EmptyClient = Omit<Client, 'id' | 'updatedAt' | 'createdAt'> & {
  clientType: ClientType
  companyName?: string
  firstName?: string
  middleName?: string
  lastName?: string
  email: string
  phone: string
  altPhone?: string
  address: string
  city: string
  country: string
  postalCode: string
  nationalId?: string
  companyRegistrationNo?: string
  taxIdNumber?: string
  contactPersonName?: string
  contactPersonDesignation?: string
  tenantIds: string[]
  companyIds: string[]
}
