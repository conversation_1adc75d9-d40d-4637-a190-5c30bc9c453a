<script setup lang="ts">
import {type PropType} from 'vue'
import {type Client, ClientType} from '../types'
import {<PERSON>aButton, VaCard, VaCardContent, VaDivider, VaInnerLoading, VaBadge} from "vuestic-ui";
import {formatDateTime} from "../../../services/utils";

// Helper function to get client display name
const getClientName = (client: Client) => {
    if (client.clientType === 'COMPANY') {
        return client.companyName || 'Unknown Company'
    } else {
        return `${client.firstName || ''} ${client.lastName || ''}`.trim() || 'Unknown Individual'
    }
}

defineProps({
    clients: {
        type: Array as PropType<Client[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
})

defineEmits<{
    (event: 'edit', client: Client): void
    (event: 'view', client: Client): void
    (event: 'delete', client: Client): void
}>()
</script>

<template>
    <VaInnerLoading
        v-if="clients.length > 0 || loading"
        :loading="loading"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-h-[4rem]"
    >
        <VaCard
            v-for="client in clients"
            :key="client.id"
            :style="{ '--va-card-outlined-border': '1px solid var(--va-background-element)' }"
            outlined
        >
            <VaCardContent class="flex flex-col h-full">
                <div class="flex justify-between items-center mb-4">
                    <VaBadge
                        :text="client.clientType === 'COMPANY' ? 'Company' : 'Individual'"
                        :color="client.clientType === 'COMPANY' ? 'info' : 'success'"
                    />
                </div>
                <div class="flex flex-col items-center gap-3 grow">
                    <h4 class="va-h4 text-center self-stretch overflow-hidden line-clamp-2 text-ellipsis">
                        {{ getClientName(client) }}
                    </h4>

                    <!-- Individual specific info -->
                    <div v-if="client.clientType === 'INDIVIDUAL' && client.nationalId" class="text-center">
                        <span class="text-[var(--va-secondary)]">ID: </span>
                        <span>{{ client.nationalId }}</span>
                    </div>

                    <!-- Company specific info -->
                    <div v-if="client.clientType === 'COMPANY' && client.contactPersonName" class="text-center">
                        <span class="text-[var(--va-secondary)]">Contact: </span>
                        <span>{{ client.contactPersonName }}</span>
                    </div>

                    <div class="text-center">
                        <span class="text-[var(--va-secondary)]">Phone: </span>
                        <span>{{ client.phone }}</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[var(--va-secondary)]">Email: </span>
                        <span class="text-sm">{{ client.email }}</span>
                    </div>
                    <div class="text-center">
                        <span class="text-[var(--va-secondary)]">Location: </span>
                        <span>{{ client.city }}, {{ client.country }}</span>
                    </div>
                    <!-- Tenant associations -->
                    <div v-if="client.tenants && client.tenants.length > 0" class="text-center">
                        <span class="text-[var(--va-secondary)]">Tenants: </span>
                        <div class="flex flex-wrap gap-1 justify-center mt-1">
                            <VaBadge
                                v-for="tenant in client.tenants"
                                :key="tenant.id"
                                :text="tenant.name"
                                color="primary"
                                size="small"
                            />
                        </div>
                    </div>

                    <!-- Company associations -->
                    <div v-if="client.companies && client.companies.length > 0" class="text-center">
                        <span class="text-[var(--va-secondary)]">Companies: </span>
                        <div class="flex flex-wrap gap-1 justify-center mt-1">
                            <VaBadge
                                v-for="company in client.companies"
                                :key="company.id"
                                :text="company.name"
                                color="secondary"
                                size="small"
                            />
                        </div>
                    </div>

                    <div class="text-center text-sm">
                        <span class="text-[var(--va-secondary)]">Created: </span>
                        <span>{{ formatDateTime(client.createdAt) }}</span>
                    </div>
                </div>
                <VaDivider class="my-6"/>
                <div class="flex justify-between">
                    <VaButton preset="secondary" icon="mso-visibility" color="info" @click="$emit('view', client)"/>
                    <VaButton preset="secondary" icon="mso-edit" color="secondary" @click="$emit('edit', client)"/>
                    <VaButton preset="secondary" icon="mso-delete" color="danger"
                              @click="$emit('delete', client)"/>
                </div>
            </VaCardContent>
        </VaCard>
    </VaInnerLoading>
    <div v-else class="p-4 flex justify-center items-center text-[var(--va-secondary)]">No clients</div>
</template>
