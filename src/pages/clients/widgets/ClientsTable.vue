<script setup lang="ts">
import {computed, PropType} from 'vue'
import {defineVaDataTableColumns, VaButton, VaPagination, VaSelect, VaBadge} from 'vuestic-ui'
import {Client, ClientType} from '../types'
import {Pagination, Sorting} from '../../../data/pages/clients'
import {useVModel} from '@vueuse/core'
import {formatDateTime} from "../../../services/utils";

const columns = defineVaDataTableColumns([
    {label: 'Name', key: 'name', sortable: true},
    {label: 'Type', key: 'clientType', sortable: true},
    {label: 'National Id', key: 'nationalId', sortable: true},
    {label: 'Email', key: 'email', sortable: true},
    {label: 'Phone', key: 'phone', sortable: true},
    {label: 'Tenants', key: 'tenants', sortable: false},
    {label: 'Companies', key: 'companies', sortable: false},
    {label: 'Created', key: 'createdAt', sortable: true},
    {label: ' ', key: 'actions'},
])

// Helper function to get client display name
const getClientName = (client: Client) => {
    if (client.clientType === 'COMPANY') {
        return client.companyName || 'Unknown Company'
    } else {
        return `${client.firstName || ''} ${client.middleName || ''} ${client.lastName || ''}`.trim() || 'Unknown Individual'
    }
}

const props = defineProps({
    clients: {
        type: Array as PropType<Client[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
    sortBy: {
        type: String as PropType<Sorting['sortBy']>,
        default: undefined,
    },
    sortingOrder: {
        type: String as PropType<Sorting['sortingOrder']>,
        default: undefined,
    },
    pagination: {
        type: Object as PropType<Pagination>,
        required: true,
    },
})

const emit = defineEmits<{
    (event: 'edit', client: Client): void
    (event: 'view', client: Client): void
    (event: 'delete', client: Client): void
}>()

const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))
</script>

<template>
    <div>
        <VaDataTable
            v-model:sort-by="sortByVModel"
            v-model:sorting-order="sortingOrderVModel"
            :items="clients"
            :columns="columns"
            :loading="loading"
        >
            <template #cell(clientType)="{ rowData: client }">
                <VaBadge
                    :text="client.clientType === 'COMPANY' ? 'Company' : 'Individual'"
                    :color="client.clientType === 'COMPANY' ? 'info' : 'success'"
                />
            </template>

            <template #cell(name)="{ rowData: client }">
                <div class="ellipsis max-w-[230px] lg:max-w-[450px]">
                    {{ getClientName(client) }}
                </div>
            </template>

            <template #cell(nationalId)="{ rowData: client }">
                {{ client.nationalId }}
            </template>

            <template #cell(email)="{ rowData: client }">
                {{ client.email }}
            </template>

            <template #cell(phone)="{ rowData: client }">
                {{ client.phone }}
            </template>

            <template #cell(city)="{ rowData: client }">
                {{ client.city }}
            </template>

            <template #cell(tenants)="{ rowData: client }">
                <div class="flex flex-wrap gap-1">
                    <VaBadge
                        v-for="tenant in client.tenants"
                        :key="tenant.id"
                        :text="tenant.name"
                        color="primary"
                        size="small"
                    />
                    <span v-if="!client.tenants || client.tenants.length === 0" class="text-gray-400">None</span>
                </div>
            </template>

            <template #cell(companies)="{ rowData: client }">
                <div class="flex flex-wrap gap-1">
                    <VaBadge
                        v-for="company in client.companies"
                        :key="company.id"
                        :text="company.name"
                        color="secondary"
                        size="small"
                    />
                    <span v-if="!client.companies || client.companies.length === 0" class="text-gray-400">None</span>
                </div>
            </template>

            <template #cell(createdAt)="{ rowData: client }">
                {{ formatDateTime(client.createdAt) }}
            </template>

            <template #cell(actions)="{ rowData: client }">
                <div class="flex gap-2 justify-end">
                    <VaButton
                        preset="primary"
                        size="small"
                        color="info"
                        icon="mso-visibility"
                        aria-label="View client"
                        @click="$emit('view', client as Client)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        color="primary"
                        icon="mso-edit"
                        aria-label="Edit client"
                        @click="$emit('edit', client as Client)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        icon="mso-delete"
                        color="danger"
                        aria-label="Delete client"
                        @click="$emit('delete', client as Client)"
                    />
                </div>
            </template>
        </VaDataTable>
        <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
            <div>
                <b>{{ $props.pagination.total }} results.</b>
                Results per page
                <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]"/>
            </div>

            <div v-if="totalPages > 1" class="flex">
                <VaButton
                    preset="secondary"
                    icon="va-arrow-left"
                    aria-label="Previous page"
                    :disabled="$props.pagination.page === 1"
                    @click="$props.pagination.page--"
                />
                <VaButton
                    class="mr-2"
                    preset="secondary"
                    icon="va-arrow-right"
                    aria-label="Next page"
                    :disabled="$props.pagination.page === totalPages"
                    @click="$props.pagination.page++"
                />
                <VaPagination
                    v-model="$props.pagination.page"
                    buttons-preset="secondary"
                    :pages="totalPages"
                    :visible-pages="5"
                    :boundary-links="false"
                    :direction-links="false"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(tbody .va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>
