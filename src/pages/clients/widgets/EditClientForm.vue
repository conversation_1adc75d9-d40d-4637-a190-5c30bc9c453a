<script setup lang="ts">
import {computed, ref, watch, onMounted} from 'vue'
import {EmptyClient, Client, ClientType, Tenant, Company} from '../types'
import {VaButton, VaForm, VaInput, VaSelect, VaAlert} from "vuestic-ui"
import {getAvailableTenants, getAvailableCompanies, checkClientExists} from '../../../data/pages/clients'
import { authService } from '../../../services/auth.service'
import { jwtDecode } from 'jwt-decode'

const props = defineProps<{
    client: Client | null
    saveButtonLabel: string
    readonly?: boolean
}>()

const emit = defineEmits<{
    (event: 'save', client: Client): void
    (event: 'close'): void
}>()

const defaultNewClient: EmptyClient = {
    clientType: ClientType.INDIVIDUAL,
    companyName: '',
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    phone: '',
    altPhone: '',
    address: '',
    city: '',
    country: '',
    postalCode: '',
    nationalId: '',
    companyRegistrationNo: '',
    taxIdNumber: '',
    contactPersonName: '',
    contactPersonDesignation: '',
    tenantIds: [],
    companyIds: [],
}

const newClient = ref<Client | EmptyClient>({...defaultNewClient})

const clientTypeOptions = [
    { text: 'Individual', value: ClientType.INDIVIDUAL },
    { text: 'Company', value: ClientType.COMPANY },
]

// Available options for dropdowns
const availableTenants = ref<Tenant[]>([])
const availableCompanies = ref<Company[]>([])

// Client existence checking
const isCheckingClient = ref(false)
const existingClientFound = ref<Client | null>(null)
const showExistingClientAlert = ref(false)

// Watch client type changes
watch(() => newClient.value.clientType, (newType) => {
    // Client type changed - reset form state if needed
}, { immediate: true })

const isFormHasUnsavedChanges = computed(() => {
    if (!props.client) {
        // Creating new - check if any field is different from empty
        return Object.keys(defaultNewClient).some(key =>
            newClient.value[key as keyof EmptyClient] !== defaultNewClient[key as keyof EmptyClient]
        )
    }
    // Editing existing - check if any field is different from original
    return Object.keys(newClient.value).some(key =>
        newClient.value[key as keyof Client] !== props.client![key as keyof Client]
    )
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.client,
    () => {
        if (!props.client) {
            newClient.value = {...defaultNewClient}
            return
        }

        newClient.value = {
            ...props.client,
            // Convert tenant and company objects to IDs for form
            tenantIds: props.client.tenants?.map(t => t.id) || [],
            companyIds: props.client.companies?.map(c => c.id) || [],
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'
const requiredArray = (v: any[]) => (v && v.length > 0) || 'At least one selection is required'

// Conditional validation based on client type
const requiredForCompany = (v: string) => {
    if (newClient.value.clientType === 'COMPANY') {
        return !!v || 'This field is required for company clients'
    }
    return true
}

const requiredForIndividual = (v: string) => {
    if (newClient.value.clientType === 'INDIVIDUAL') {
        return !!v || 'This field is required for individual clients'
    }
    return true
}

const handleSave = () => {
    const formData = {
        ...newClient.value,
    }

    let clientToSave
    if (props.client) {
        // Editing: preserve all fields, update with form values
        clientToSave = {...props.client, ...formData}
    } else if (existingClientFound.value) {
        // Updating existing client found during creation
        clientToSave = {...existingClientFound.value, ...formData}
    } else {
        // Creating: use form values
        clientToSave = formData
    }

    emit('save', clientToSave as Client)
}

// Get current user's tenant from JWT token
const getCurrentUserTenant = () => {
    try {
        const token = authService.getAuthToken()
        if (token) {
            const decoded: any = jwtDecode(token)
            // For now, we'll assume the first tenant is the user's default
            // This should be enhanced to get actual user-tenant relationships
            return decoded.tenantId || null
        }
    } catch (error) {
        // Error decoding token - use fallback
    }
    return null
}

// Load available options on mount
const loadOptions = async () => {
    try {
        const [tenants, companies] = await Promise.all([
            getAvailableTenants(),
            getAvailableCompanies()
        ])

        // Check if the response has a data property (API response wrapper)
        availableTenants.value = tenants.data || tenants
        availableCompanies.value = companies.data || companies

        // Auto-select user's tenant for new clients
        if (!props.client && availableTenants.value.length > 0) {
            const userTenantId = getCurrentUserTenant()
            if (userTenantId && availableTenants.value.find(t => t.id === userTenantId)) {
                newClient.value.tenantIds = [userTenantId]
            } else {
                // If no specific tenant found, select the first one as default
                newClient.value.tenantIds = [availableTenants.value[0].id]
            }
        }

        // Auto-select first company for new clients if available
        if (!props.client && availableCompanies.value.length > 0) {
            newClient.value.companyIds = [availableCompanies.value[0].id]
        }
    } catch (error) {
        // Set empty arrays as fallback
        availableTenants.value = []
        availableCompanies.value = []
    }
}

// Check if client exists based on national ID or company name
const checkIfClientExists = async () => {
    if (props.client) return // Don't check for existing clients when editing

    const nationalId = newClient.value.nationalId?.trim()
    const companyName = newClient.value.companyName?.trim()

    if (!nationalId && !companyName) return

    try {
        isCheckingClient.value = true
        const response = await checkClientExists(nationalId, companyName)

        if (response.data.exists) {
            existingClientFound.value = response.data.client
            showExistingClientAlert.value = true

            // Pre-populate form with existing client data
            const existingClient = response.data.client
            newClient.value = {
                ...existingClient,
                // Convert tenant and company objects to IDs for form
                tenantIds: existingClient.tenants?.map((t: any) => t.id) || [],
                companyIds: existingClient.companies?.map((c: any) => c.id) || [],
            }
        } else {
            existingClientFound.value = null
            showExistingClientAlert.value = false
        }
    } catch (error) {
        // Error checking client existence - continue silently
    } finally {
        isCheckingClient.value = false
    }
}

// Watch for changes in national ID or company name to trigger existence check
watch(() => newClient.value.nationalId, (newValue) => {
    if (newValue && newValue.length >= 3 && newClient.value.clientType === 'INDIVIDUAL') {
        checkIfClientExists()
    }
}, { debounce: 500 })

watch(() => newClient.value.companyName, (newValue) => {
    if (newValue && newValue.length >= 3 && newClient.value.clientType === 'COMPANY') {
        checkIfClientExists()
    }
}, { debounce: 500 })

// Reset the existing client alert when a client type changes
watch(() => newClient.value.clientType, () => {
    showExistingClientAlert.value = false
    existingClientFound.value = null
})

onMounted(() => {
    loadOptions()
})
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <!-- Client Type Selection -->
        <VaSelect
            v-model="newClient.clientType"
            label="Client Type"
            :options="clientTypeOptions"
            text-by="text"
            value-by="value"
            :rules="[required]"
            :readonly="props.readonly"
            class="mb-4"
        />

        <!-- Existing Client Alert -->
        <VaAlert
            v-if="showExistingClientAlert && existingClientFound"
            color="warning"
            icon="warning"
            class="mb-4"
            closeable
            @close="showExistingClientAlert = false"
        >
            <template #title>
                Client Already Exists
            </template>
            <p class="mb-2">
                A {{ existingClientFound.clientType.toLowerCase() }} client
                <strong>{{ existingClientFound.clientType === 'COMPANY' ? existingClientFound.companyName : `${existingClientFound.firstName} ${existingClientFound.lastName}` }}</strong>
                already exists with this {{ existingClientFound.clientType === 'COMPANY' ? 'company name' : 'national ID' }}.
            </p>
            <p class="text-sm">
                The form has been pre-populated with the existing client's information.
                You can add additional tenant/company associations and save to update the client.
            </p>
        </VaAlert>

        <!-- Loading indicator for client check -->
        <VaAlert
            v-if="isCheckingClient"
            color="info"
            icon="hourglass_empty"
            class="mb-4"
        >
            Checking if client exists...
        </VaAlert>



        <!-- Company Fields (shown only for COMPANY type) -->
        <div v-if="newClient.clientType === 'COMPANY'" class="space-y-4">
            <h3 class="text-lg font-semibold text-primary">Company Information</h3>
            <VaInput
                v-model="newClient.companyName"
                label="Company Name"
                :rules="[requiredForCompany]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.companyRegistrationNo"
                label="Company Registration Number (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.contactPersonName"
                label="Contact Person Name (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.contactPersonDesignation"
                label="Contact Person Designation (Optional)"
                :readonly="props.readonly"
            />
        </div>

        <!-- Individual Fields (shown only for INDIVIDUAL type) -->
        <div v-if="newClient.clientType === 'INDIVIDUAL'" class="space-y-4">
            <h3 class="text-lg font-semibold text-primary">Personal Information</h3>
            <VaInput
                v-model="newClient.firstName"
                label="First Name"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.middleName"
                label="Middle Name (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.lastName"
                label="Last Name"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.nationalId"
                label="National ID"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
        </div>

        <!-- Common Contact Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Contact Information</h3>
            <VaInput
                v-model="newClient.email"
                label="Email"
                type="email"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.phone"
                label="Phone"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.altPhone"
                label="Alternative Phone (Optional)"
                :readonly="props.readonly"
            />
        </div>

        <!-- Address Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Address Information</h3>
            <VaInput
                v-model="newClient.address"
                label="Street Address"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.city"
                label="City"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.country"
                label="Country"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.postalCode"
                label="Postal Code"
                :rules="[required]"
                :readonly="props.readonly"
            />
        </div>

        <!-- Optional Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Additional Information (Optional)</h3>
            <VaInput
                v-model="newClient.taxIdNumber"
                label="Tax ID Number"
                :readonly="props.readonly"
            />
        </div>

        <!-- Tenant and Company Associations -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Tenants and Companies (Required)</h3>

            <VaSelect
                v-model="newClient.tenantIds"
                label="Tenants"
                :options="availableTenants"
                text-by="name"
                value-by="id"
                track-by="id"
                multiple
                searchable
                :rules="[requiredArray]"
                :readonly="props.readonly"
                placeholder="Select at least one tenant"
                :loading="availableTenants.length === 0"
            />

            <VaSelect
                v-model="newClient.companyIds"
                label="Companies"
                :options="availableCompanies"
                text-by="name"
                value-by="id"
                track-by="id"
                multiple
                searchable
                :rules="[requiredArray]"
                :readonly="props.readonly"
                placeholder="Select at least one company"
                :loading="availableCompanies.length === 0"
            />
        </div>

        <!-- Action Buttons -->
        <div v-if="!props.readonly" class="flex justify-end flex-col-reverse sm:flex-row mt-6 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
        <div v-else class="flex justify-end flex-col-reverse sm:flex-row mt-6 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Close</VaButton>
        </div>
    </VaForm>
</template>
