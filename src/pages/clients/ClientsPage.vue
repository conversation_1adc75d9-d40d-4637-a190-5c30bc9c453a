<script setup lang="ts">
import {ref} from 'vue'
import {useModal, useToast, VaButton, Va<PERSON>uttonToggle, Va<PERSON>ard, VaCardContent, VaModal} from 'vuestic-ui'
import {Client} from './types'
import {useLocalStorage} from '@vueuse/core'
import {useClients} from './composables/useClients'
import ClientCards from './widgets/ClientCards.vue'
import ClientsTable from './widgets/ClientsTable.vue'
import EditClientForm from './widgets/EditClientForm.vue'

const doShowAsCards = useLocalStorage('clients-view', false)

const {clients, add, update, remove, isLoading, fetch, pagination, sorting} = useClients()

const clientToEdit = ref<Client | null>(null)
const doShowClientFormModal = ref(false)
const isViewMode = ref(false)

const editClient = (client: Client) => {
    clientToEdit.value = client
    isViewMode.value = false
    doShowClientFormModal.value = true
}

const viewClient = (client: Client) => {
    clientToEdit.value = client
    isViewMode.value = true
    doShowClientFormModal.value = true
}

const createNewClient = () => {
    clientToEdit.value = null
    isViewMode.value = false
    doShowClientFormModal.value = true
}

const {init: notify} = useToast()

const onClientSaved = async (client: Client) => {
    try {
        if ('id' in client && client.id) {
            await update(client as Client)
            notify({
                message: `${client.clientType === 'COMPANY' ? 'Company' : 'Individual'} client updated successfully`,
                color: 'success',
            })
        } else {
            await add(client as Client)
            notify({
                message: `${client.clientType === 'COMPANY' ? 'Company' : 'Individual'} client created successfully`,
                color: 'success',
            })
        }
        doShowClientFormModal.value = false
        // Refresh the client list after successful save
        await fetch()
    } catch (error: any) {
        // Extract the error message from the backend response
        let errorMessage = 'An error occurred while saving the client'

        if (error.response?.data?.message) {
            // Backend error response
            errorMessage = error.response.data.message
        } else if (error.message) {
            // Axios or other error
            errorMessage = error.message
        }

        notify({
            message: errorMessage,
            color: 'danger',
        })

        // Refresh the client list even after error to ensure data consistency
        await fetch()
    }
}

const {confirm} = useModal()

const onClientDeleted = async (client: Client) => {
    const clientName = client.clientType === 'COMPANY' ? client.companyName : `${client.firstName} ${client.lastName}`
    const response = await confirm({
        title: 'Delete Client',
        message: `Are you sure you want to delete ${client.clientType.toLowerCase()} client "${clientName}"?`,
        okText: 'Delete',
        size: 'small',
        maxWidth: '380px',
    })

    if (!response) {
        return
    }

    try {
        await remove(client)
        notify({
            message: `${client.clientType === 'COMPANY' ? 'Company' : 'Individual'} client deleted successfully`,
            color: 'success',
        })
    } catch (error: any) {
        notify({
            message: error.message || 'An error occurred while deleting the client',
            color: 'danger',
        })
    }
}

const editFormRef = ref()

const beforeEditFormModalClose = async (hide: () => unknown) => {
    if (editFormRef.value.isFormHasUnsavedChanges) {
        const agreed = await confirm({
            maxWidth: '380px',
            message: 'Form has unsaved changes. Are you sure you want to close it?',
            size: 'small',
        })
        if (agreed) {
            hide()
        }
    } else {
        hide()
    }
}
</script>

<template>
    <h6 class="page-title">Clients</h6>

    <VaCard>
        <VaCardContent>
            <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                <div class="flex flex-col md:flex-row gap-2 justify-start">
                    <VaButtonToggle
                        v-model="doShowAsCards"
                        color="background-element"
                        border-color="background-element"
                        :options="[{ label: 'Cards', value: true }, { label: 'Table', value: false },]"
                    />
                </div>
                <VaButton icon="add" color="primary" @click="createNewClient">Client</VaButton>
            </div>

            <ClientCards
                v-if="doShowAsCards"
                :clients="clients"
                :loading="isLoading"
                @view="viewClient"
                @edit="editClient"
                @delete="onClientDeleted"
            />
            <ClientsTable
                v-else
                v-model:sort-by="sorting.sortBy"
                v-model:sorting-order="sorting.sortingOrder"
                v-model:pagination="pagination"
                :clients="clients"
                :loading="isLoading"
                @view="viewClient"
                @edit="editClient"
                @delete="onClientDeleted"
            />
        </VaCardContent>

        <VaModal
            v-slot="{ cancel, ok }"
            v-model="doShowClientFormModal"
            size="small"
            mobile-fullscreen
            close-button
            stateful
            hide-default-actions
            :before-cancel="beforeEditFormModalClose"
        >
            <h1 v-if="clientToEdit === null" class="va-h5 mb-4">Add Client</h1>
            <h1 v-else-if="isViewMode" class="va-h5 mb-4">View Client</h1>
            <h1 v-else class="va-h5 mb-4">Edit Client</h1>
            <EditClientForm
                ref="editFormRef"
                :client="clientToEdit"
                :save-button-label="clientToEdit === null ? 'Add' : 'Save'"
                :readonly="isViewMode"
                @close="cancel"
                @save="(client) => {
                    onClientSaved(client)
                    ok()
                }"
            />
        </VaModal>
    </VaCard>
</template>
