import { computed, ref, Ref, unref } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/clients';
import { Client } from '../types';
import { useClientsStore } from '../../../stores/clientStores';

const makePaginationRef = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 });
const makeSortingRef = () => ref<Sorting>({ sortBy: 'createdAt', sortingOrder: 'DESC' });

export const useClients = (options?: { sorting?: Ref<Sorting>; pagination?: Ref<Pagination> }) => {
    const isLoading = ref(false);
    const clientsStore = useClientsStore();

    const { sorting = makeSortingRef(), pagination = makePaginationRef() } = options ?? {};

    const fetch = async () => {
        isLoading.value = true;
        await clientsStore.getAll({
            sorting: unref(sorting),
            pagination: unref(pagination),
        });
        pagination.value = clientsStore.pagination;
        isLoading.value = false;
    };

    const clients = computed(() => {
        const paginated = clientsStore.items.slice(
            (pagination.value.page - 1) * pagination.value.perPage,
            pagination.value.page * pagination.value.perPage,
        );

        const getSortItem = (obj: any, sortBy: Sorting['sortBy']) => {
            if (sortBy === 'name') {
                // Sort by the appropriate name field based on client type
                if (obj.clientType === 'COMPANY') {
                    return obj.companyName || '';
                } else {
                    return `${obj.firstName || ''} ${obj.lastName || ''}`.trim();
                }
            }

            if (sortBy === 'updatedAt') {
                 return new Date(obj[sortBy]);
            }

            if (sortBy === 'createdAt') {
                return new Date(obj[sortBy]);
            }

            return obj[sortBy];
        };

        if (sorting.value.sortBy && sorting.value.sortingOrder) {
            paginated.sort((a, b) => {
                a = getSortItem(a, sorting.value.sortBy!);
                b = getSortItem(b, sorting.value.sortBy!);

                if (a < b) {
                    return sorting.value.sortingOrder === 'asc' ? -1 : 1;
                }
                if (a > b) {
                    return sorting.value.sortingOrder === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }

        return paginated;
    });

    fetch();

    return {
        isLoading,
        clients,
        fetch,
        async add(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) {
            isLoading.value = true;
            await clientsStore.add(client);
            await fetch();
            isLoading.value = false;
        },

        async update(client: Client) {
            isLoading.value = true;
            await clientsStore.update(client);
            await fetch();
            isLoading.value = false;
        },

        async remove(client: Client) {
            isLoading.value = true;
            await clientsStore.remove(client);
            await fetch();
            isLoading.value = false;
        },
        pagination,
        sorting,
    };
};
