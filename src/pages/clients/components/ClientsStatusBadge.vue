<script setup lang="ts">
import {VaBadge} from "vuestic-ui";
import {computed} from "vue";

const props = defineProps({
    status: {
        type: Boolean,
        required: true,
    },
})

const badgeColor = computed(() => props.status ? 'success' : 'secondary')
const badgeText = computed(() => props.status ? 'ACTIVE' : 'INACTIVE')
</script>

<template>
    <VaBadge square :color="badgeColor" :text="badgeText"/>
</template>
