import { computed, ref, Ref, unref, watch } from 'vue';
import { v4 as uuid } from 'uuid';
import type { Filters, Pagination, Sorting } from '../../../data/pages/roles';
import { Role } from '../types';
import { useRolesStore } from '../../../stores/roles';

const makePaginationRef = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 });
const makeSortingRef = () => ref<Sorting>({ sortBy: 'name', sortingOrder: null });
const makeFiltersRef = () => ref<Partial<Filters>>({ isActive: true, search: '' });

export const useRoles = (options?: {
    pagination?: Ref<Pagination>
    sorting?: Ref<Sorting>
    filters?: Ref<Partial<Filters>>
}) => {
    const isLoading = ref(false);
    const error = ref();
    const rolesStore = useRolesStore();
    const { filters = makeFiltersRef(), sorting = makeSortingRef(), pagination = makePaginationRef(), } = options || {};

    const fetch = async () => {
        isLoading.value = true;
        try {
            await rolesStore.getAll({
                filters: unref(filters),
                sorting: unref(sorting),
                pagination: unref(pagination),
            });
            pagination.value = rolesStore.pagination;
        } finally {
            isLoading.value = false;
        }
    };

    watch(
        filters,
        () => {
            // Reset pagination to first page when filters changed
            pagination.value.page = 1;
            fetch();
        },
        { deep: true },
    );

    fetch();

    const roles = computed(() => {
        const getSortItem = (obj: any, sortBy: string) => {
            if (sortBy === 'permissions') {
                return obj.permissions.map((project: any) => project).join(', ');
            }

            return obj[sortBy];
        };

        const paginated = rolesStore.items.slice(
            (pagination.value.page - 1) * pagination.value.perPage,
            pagination.value.page * pagination.value.perPage,
        );

        if (sorting.value.sortBy && sorting.value.sortingOrder) {
            paginated.sort((a, b) => {
                const first = getSortItem(a, sorting.value.sortBy!);
                const second = getSortItem(b, sorting.value.sortBy!);
                if (first > second) {
                    return sorting.value.sortingOrder === 'asc' ? 1 : -1;
                }
                if (first < second) {
                    return sorting.value.sortingOrder === 'asc' ? -1 : 1;
                }
                return 0;
            });
        }
        return paginated;
    });

    return {
        error,
        isLoading,
        filters,
        sorting,
        pagination,
        roles,
        fetch,
        async add(role: Role) {
            isLoading.value = true;
            try {
                return await rolesStore.add(role);
            } catch (e) {
                error.value = e;
            } finally {
                isLoading.value = false;
            }
        },

        async update(role: Role) {
            isLoading.value = true;
            try {
                return await rolesStore.update(role);
            } catch (e) {
                error.value = e;
            } finally {
                isLoading.value = false;
            }
        },

        async remove(role: Role) {
            isLoading.value = true;
            try {
                return await rolesStore.remove(role);
            } catch (e) {
                error.value = e;
            } finally {
                isLoading.value = false;
            }
        },

        async uploadAvatar(avatar: Blob) {
            const formData = new FormData();
            formData.append('avatar', avatar);
            formData.append('id', uuid());

            return rolesStore.uploadAvatar(formData);
        },
    };
};
