<script setup lang="ts">
import { computed, PropType, ref, watch, onMounted } from 'vue';
import { useForm } from 'vuestic-ui';
import { Role, Permission } from '../types';
import { validators } from '../../../services/utils';
import api from '../../../services/api';
import { authService } from '../../../services/auth.service';
import axios from 'axios';

const props = defineProps({
    role: {
        type: Object as PropType<Role | null>,
        default: null,
    },
    saveButtonLabel: {
        type: String,
        default: 'Save',
    },
});

const defaultNewRole: Omit<Role, 'id' | 'createdAt' | 'updatedAt'> = {
    name: '',
    description: '',
    permissions: [],
};

const newRole = ref<Role>({ ...defaultNewRole } as Role);
const availablePermissions = ref<Permission[]>([]);

// Fetch available permissions
const fetchPermissions = async () => {
    try {
        const response = await axios.get(api.allPermissions(), {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        });
        availablePermissions.value = response.data;
    } catch (error) {
        console.error('Error fetching permissions:', error);
    }
};

onMounted(() => {
    fetchPermissions();
});

const isFormHasUnsavedChanges = computed(() => {
    return Object.keys(newRole.value).some((key) => {
        if (key === 'id' || key === 'createdAt' || key === 'updatedAt') {
            return false;
        }

        return (
            newRole.value[key as keyof Omit<Role, 'id' | 'createdAt' | 'updatedAt'>] !==
            (props.role ?? defaultNewRole)?.[key as keyof Omit<Role, 'id' | 'createdAt' | 'updatedAt'>]
        );
    });
});

defineExpose({
    isFormHasUnsavedChanges,
});

watch(
    () => props.role,
    () => {
        if (props.role) {
            newRole.value = {
                ...props.role,
            };
        } else {
            newRole.value = { ...defaultNewRole } as Role;
        }
    },
    { immediate: true },
);

const form = useForm('add-role-form');

const emit = defineEmits(['close', 'save']);

const onSave = () => {
    if (form.validate()) {
        emit('save', newRole.value);
    }
};

const selectedPermissionIds = computed({
    get: () => newRole.value.permissions?.map(p => p.id) || [],
    set: (value: string[]) => {
        newRole.value.permissions = availablePermissions.value.filter(p => value.includes(p.id));
    }
});
</script>

<template>
    <VaForm v-slot="{ isValid }" ref="add-role-form"
            class="flex-col justify-start items-start gap-4 inline-flex w-full">
        <div class="self-stretch flex-col justify-start items-start gap-4 flex">
            <div class="flex gap-4 flex-col sm:flex-row w-full">
                <VaInput
                    v-model="newRole.name"
                    label="Role Name"
                    class="w-full sm:w-1/2"
                    :rules="[validators.required]"
                    name="name"
                />
                <VaInput
                    v-model="newRole.description"
                    label="Description"
                    class="w-full sm:w-1/2"
                    name="description"
                />
            </div>

            <div class="w-full">
                <VaSelect
                    v-model="selectedPermissionIds"
                    label="Permissions"
                    class="w-full"
                    :options="availablePermissions"
                    text-by="name"
                    value-by="id"
                    multiple
                    searchable
                    name="permissions"
                />
            </div>

            <div class="flex gap-2 flex-col-reverse items-stretch justify-end w-full sm:flex-row sm:items-center">
                <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
                <VaButton :disabled="!isValid" @click="onSave">{{ saveButtonLabel }}</VaButton>
            </div>
        </div>
    </VaForm>
</template>
