<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import UsersTable from './widgets/UsersTable.vue';
import EditUserForm from './widgets/EditUserForm.vue';
import { User } from './types';
import { useUsers } from './composables/useUsers';
import { useModal, useToast } from 'vuestic-ui';
import { useRoles } from '../roles/composables/useRoles';

const doShowEditUserModal = ref(false);

const { users, isLoading, filters, sorting, pagination, error, ...usersApi } = useUsers();
const { roles } = useRoles();

const userToEdit = ref<User | null>(null);

const showEditUserModal = (user: User) => {
    userToEdit.value = user;
    doShowEditUserModal.value = true;
};

const showAddUserModal = () => {
    userToEdit.value = null;
    doShowEditUserModal.value = true;
};

const { init: notify } = useToast();

watchEffect(() => {
    if (error.value) {
        notify({
            message: error.value.message,
            color: 'danger',
        });
    }
});

const onUserSaved = async (user: User) => {
    if (user.avatar.startsWith('blob:')) {
        const blob = await fetch(user.avatar).then((r) => r.blob());
        const { publicUrl } = await usersApi.uploadAvatar(blob);
        user.avatar = publicUrl;
    }

    if (userToEdit.value) {
        await usersApi.update(user);
        if (!error.value) {
            notify({
                message: `${user.firstName} ${user.lastName} has been updated`,
                color: 'success',
            });
        }
    } else {
        await usersApi.add(user);

        if (!error.value) {
            notify({
                message: `${user.firstName} ${user.lastName} has been created`,
                color: 'success',
            });
        }
    }
};

const onUserDelete = async (user: User) => {
    await usersApi.remove(user);
    notify({
        message: `${user.firstName} ${user.lastName} has been deleted`,
        color: 'success',
    });
};

const editFormRef = ref();

const { confirm } = useModal();

const beforeEditFormModalClose = async (hide: () => unknown) => {
    if (editFormRef.value.isFormHasUnsavedChanges) {
        const agreed = await confirm({
            maxWidth: '380px',
            message: 'Form has unsaved changes. Are you sure you want to close it?',
            size: 'small',
        });
        if (agreed) {
            hide();
        }
    } else {
        hide();
    }
};
</script>

<template>
    <h1 class="page-title">Users</h1>

    <VaCard>
        <VaCardContent>
            <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                <div class="flex flex-col md:flex-row gap-2 justify-start">
                    <VaButtonToggle
                        v-model="filters.isActive"
                        color="background-element"
                        border-color="background-element"
                        :options="[
              { label: 'Active', value: true },
              { label: 'Inactive', value: false },
            ]"
                    />
                    <VaInput v-model="filters.search" placeholder="Search">
                        <template #prependInner>
                            <VaIcon name="search" color="secondary" size="small" />
                        </template>
                    </VaInput>
                </div>
                <VaButton @click="showAddUserModal">Add User</VaButton>
            </div>

            <UsersTable
                v-model:sort-by="sorting.sortBy"
                v-model:sorting-order="sorting.sortingOrder"
                :users="users"
                :roles="roles"
                :loading="isLoading"
                :pagination="pagination"
                @editUser="showEditUserModal"
                @deleteUser="onUserDelete"
            />
        </VaCardContent>
    </VaCard>

    <VaModal
        v-slot="{ cancel, ok }"
        v-model="doShowEditUserModal"
        size="small"
        mobile-fullscreen
        close-button
        hide-default-actions
        :before-cancel="beforeEditFormModalClose"
    >
        <h1 class="va-h5">{{ userToEdit ? 'Edit user' : 'Add user' }}</h1>
        <EditUserForm
            ref="editFormRef"
            :user="userToEdit"
            :roles="roles"
            :save-button-label="userToEdit ? 'Save' : 'Add'"
            @close="cancel"
            @save="
        (user) => {
          onUserSaved(user)
          ok()
        }
      "
        />
    </VaModal>
</template>
