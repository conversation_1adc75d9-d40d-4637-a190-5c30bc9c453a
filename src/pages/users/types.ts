export type UUID = `${string}-${string}-${string}-${string}-${string}`

export type Role = {
  id: UUID
  name: string
  description: string
  createdAt: string
  updatedAt: string
}

export type Tenant = {
  id: UUID
  name: string
  code: string
  email: string
  phone: string
  address: string
  website?: string
  logoUrl?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export type Company = {
  id: UUID
  name: string
  contactPerson: string
  email: string
  phone: string
  createdAt: string
  updatedAt: string
}

export type User = {
  id: UUID
  firstName: string
  lastName: string
  email: string
  username: string
  phone: string
  isActive: boolean
  roles: Role[]
  tenants: Tenant[]
  companies: Company[]
  avatar: string
  createdAt: string
  updatedAt: string
}
