<script setup lang="ts">
import {ref} from 'vue'
import {useModal, useToast, VaButton, VaButtonToggle, VaCard, VaCardContent, VaModal} from 'vuestic-ui'
import {Company} from './types'
import {useLocalStorage} from '@vueuse/core'
import {useCompanies} from './composables/useCompanies'
import CompanyCards from './widgets/CompanyCards.vue'
import CompaniesTable from './widgets/CompaniesTable.vue'
import EditCompanyForm from './widgets/EditCompanyForm.vue'

const doShowAsCards = useLocalStorage('companies-view', false)

const {companies, add, update, remove, isLoading,  pagination, sorting} = useCompanies()

const companyToEdit = ref<Company | null>(null)
const doShowCompanyFormModal = ref(false)
const isViewMode = ref(false)

const editCompany = (company: Company) => {
    companyToEdit.value = company
    isViewMode.value = false
    doShowCompanyFormModal.value = true
}

const viewCompany = (company: Company) => {
    companyToEdit.value = company
    isViewMode.value = true
    doShowCompanyFormModal.value = true
}

const createNewCompany = () => {
    companyToEdit.value = null
    isViewMode.value = false
    doShowCompanyFormModal.value = true
}

const {init: notify} = useToast()

const onCompanySaved = async (company: Company) => {
    doShowCompanyFormModal.value = false
    if ('id' in company && company.id) {
        await update(company as Company)
        notify({
            message: 'Company updated',
            color: 'success',
        })
    } else {
        await add(company as Company)
        notify({
            message: 'Company created',
            color: 'success',
        })
    }
}

const {confirm} = useModal()

const onCompanyDeleted = async (company: Company) => {
    const response = await confirm({
        title: 'Delete Company',
        message: `Are you sure you want to delete company "${company.name}"?`,
        okText: 'Delete',
        size: 'small',
        maxWidth: '380px',
    })

    if (!response) {
        return
    }

    await remove(company)
    notify({
        message: 'Company deleted',
        color: 'success',
    })
}

const editFormRef = ref()

const beforeEditFormModalClose = async (hide: () => unknown) => {
    if (editFormRef.value.isFormHasUnsavedChanges) {
        const agreed = await confirm({
            maxWidth: '380px',
            message: 'Form has unsaved changes. Are you sure you want to close it?',
            size: 'small',
        })
        if (agreed) {
            hide()
        }
    } else {
        hide()
    }
}
</script>

<template>
    <h6 class="page-title">Companies</h6>

    <VaCard>
        <VaCardContent>
            <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                <div class="flex flex-col md:flex-row gap-2 justify-start">
                    <VaButtonToggle
                        v-model="doShowAsCards"
                        color="background-element"
                        border-color="background-element"
                        :options="[{ label: 'Cards', value: true }, { label: 'Table', value: false },]"
                    />
                </div>
                <VaButton icon="add" color="primary" @click="createNewCompany">Company</VaButton>
            </div>

            <CompanyCards
                v-if="doShowAsCards"
                :companies="companies"
                :loading="isLoading"
                @view="viewCompany"
                @edit="editCompany"
                @delete="onCompanyDeleted"
            />
            <CompaniesTable
                v-else
                v-model:sort-by="sorting.sortBy"
                v-model:sorting-order="sorting.sortingOrder"
                v-model:pagination="pagination"
                :companies="companies"
                :loading="isLoading"
                @view="viewCompany"
                @edit="editCompany"
                @delete="onCompanyDeleted"
            />
        </VaCardContent>

        <VaModal
            v-slot="{ cancel, ok }"
            v-model="doShowCompanyFormModal"
            size="small"
            mobile-fullscreen
            close-button
            stateful
            hide-default-actions
            :before-cancel="beforeEditFormModalClose"
        >
            <h1 v-if="companyToEdit === null" class="va-h5 mb-4">Add Company</h1>
            <h1 v-else-if="isViewMode" class="va-h5 mb-4">View Company</h1>
            <h1 v-else class="va-h5 mb-4">Edit Company</h1>
            <EditCompanyForm
                ref="editFormRef"
                :company="companyToEdit"
                :save-button-label="companyToEdit === null ? 'Add' : 'Save'"
                :readonly="isViewMode"
                @close="cancel"
                @save="(company) => {
                    onCompanySaved(company)
                    ok()
                }"
            />
        </VaModal>
    </VaCard>
</template>
