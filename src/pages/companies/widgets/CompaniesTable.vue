<script setup lang="ts">
import {computed, PropType} from 'vue'
import {defineVaDataTableColumns, VaButton, VaPagination, VaSelect} from 'vuestic-ui'
import {Company} from '../types'
import {Pagination, Sorting} from '../../../data/pages/companies'
import {useVModel} from '@vueuse/core'
import {formatDateTime} from "../../../services/utils";
import CompaniesStatusBadge from "../components/CompaniesStatusBadge.vue";

const columns = defineVaDataTableColumns([
    {label: 'Name', key: 'name', sortable: true},
    {label: 'Contact Person', key: 'contactPerson', sortable: true},
    {label: 'Email', key: 'email', sortable: true},
    {label: 'Phone', key: 'phone', sortable: true},
    {label: 'Update Date', key: 'updatedAt', sortable: true},
    {label: 'Creation Date', key: 'createdAt', sortable: true},
    {label: ' ', key: 'actions'},
])

const props = defineProps({
    companies: {
        type: Array as PropType<Company[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
    sortBy: {
        type: String as PropType<Sorting['sortBy']>,
        default: undefined,
    },
    sortingOrder: {
        type: String as PropType<Sorting['sortingOrder']>,
        default: undefined,
    },
    pagination: {
        type: Object as PropType<Pagination>,
        required: true,
    },
})

const emit = defineEmits<{
    (event: 'edit', company: Company): void
    (event: 'view', company: Company): void
    (event: 'delete', company: Company): void
}>()

const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))
</script>

<template>
    <div>
        <VaDataTable
            v-model:sort-by="sortByVModel"
            v-model:sorting-order="sortingOrderVModel"
            :items="companies"
            :columns="columns"
            :loading="loading"
        >
            <template #cell(name)="{ rowData }">
                <div class="ellipsis max-w-[230px] lg:max-w-[450px]">
                    {{ rowData.name }}
                </div>
            </template>

            <template #cell(contactPerson)="{ rowData: company }">
                {{ company.contactPerson }}
            </template>

            <template #cell(email)="{ rowData: company }">
                {{ company.email }}
            </template>

            <template #cell(phone)="{ rowData: company }">
                {{ company.phone }}
            </template>

            <template #cell(updatedAt)="{ rowData: company }">
                {{ formatDateTime(company.updatedAt) }}
            </template>

            <template #cell(createdAt)="{ rowData: company }">
                {{ formatDateTime(company.createdAt) }}
            </template>

            <template #cell(actions)="{ rowData: company }">
                <div class="flex gap-2 justify-end">
                    <VaButton
                        preset="primary"
                        size="small"
                        color="info"
                        icon="mso-visibility"
                        aria-label="View company"
                        @click="$emit('view', company as Company)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        color="primary"
                        icon="mso-edit"
                        aria-label="Edit company"
                        @click="$emit('edit', company as Company)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        icon="mso-delete"
                        color="danger"
                        aria-label="Delete company"
                        @click="$emit('delete', company as Company)"
                    />
                </div>
            </template>
        </VaDataTable>
        <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
            <div>
                <b>{{ $props.pagination.total }} results.</b>
                Results per page
                <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]"/>
            </div>

            <div v-if="totalPages > 1" class="flex">
                <VaButton
                    preset="secondary"
                    icon="va-arrow-left"
                    aria-label="Previous page"
                    :disabled="$props.pagination.page === 1"
                    @click="$props.pagination.page--"
                />
                <VaButton
                    class="mr-2"
                    preset="secondary"
                    icon="va-arrow-right"
                    aria-label="Next page"
                    :disabled="$props.pagination.page === totalPages"
                    @click="$props.pagination.page++"
                />
                <VaPagination
                    v-model="$props.pagination.page"
                    buttons-preset="secondary"
                    :pages="totalPages"
                    :visible-pages="5"
                    :boundary-links="false"
                    :direction-links="false"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(tbody .va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>
