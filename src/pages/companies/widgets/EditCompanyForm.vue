<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyCompany, Company} from '../types'
import {VaButton, VaForm, VaInput, VaSwitch} from "vuestic-ui"
import {formatDateForInput, formatDateFromInput} from '../../../services/utils'

const props = defineProps<{
    company: Company | null
    saveButtonLabel: string
    readonly?: boolean
}>()

const emit = defineEmits<{
    (event: 'save', company: Company): void
    (event: 'close'): void
}>()

const defaultNewCompany: EmptyCompany = {
    name: '',
    code: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    logoUrl: '',
    isActive: true,
    subscriptionPlan: '',
    subscriptionStartDate: '',
    subscriptionEndDate: '',
}

const newCompany = ref<Company | EmptyCompany>({...defaultNewCompany})

const isFormHasUnsavedChanges = computed(() => {
    if (!props.company) {
        // Creating new - check if any field is different from empty
        return Object.keys(defaultNewCompany).some(key =>
            newCompany.value[key as keyof EmptyCompany] !== defaultNewCompany[key as keyof EmptyCompany]
        )
    }
    // Editing existing - check if any field is different from original
    return Object.keys(newCompany.value).some(key =>
        newCompany.value[key as keyof Company] !== props.company![key as keyof Company]
    )
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.company,
    () => {
        if (!props.company) {
            newCompany.value = {...defaultNewCompany}
            return
        }

        newCompany.value = {
            ...props.company,
            // Convert ISO dates to YYYY-MM-DD format for HTML date inputs
            subscriptionStartDate: formatDateForInput(props.company.subscriptionStartDate || ''),
            subscriptionEndDate: formatDateForInput(props.company.subscriptionEndDate || ''),
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'

const handleSave = () => {
    // Convert dates back to ISO format for API
    const formData = {
        ...newCompany.value,
        subscriptionStartDate: newCompany.value.subscriptionStartDate
            ? formatDateFromInput(newCompany.value.subscriptionStartDate)
            : '',
        subscriptionEndDate: newCompany.value.subscriptionEndDate
            ? formatDateFromInput(newCompany.value.subscriptionEndDate)
            : '',
    }

    let companyToSave
    if (props.company) {
        // Editing: preserve all fields, update with form values
        companyToSave = {...props.company, ...formData}
    } else {
        // Creating: use form values but exclude isActive (status) as it should be auto-generated
        const {isActive, ...dataWithoutStatus} = formData
        companyToSave = dataWithoutStatus
    }

    emit('save', companyToSave as Company)
}
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newCompany.name" label="Company Name" :rules="[required]" :readonly="props.readonly"/>
        <!-- Code field only shown in view mode since it's auto-generated -->
        <VaInput v-if="props.readonly && props.company" v-model="newCompany.code" label="Company Code" readonly/>
        <VaInput v-model="newCompany.email" label="Email" type="email" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.phone" label="Phone" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.address" label="Address" :rules="[required]" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.website" label="Website (Optional)" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.logoUrl" label="Logo URL (Optional)" :readonly="props.readonly"/>
        <!-- Status field only shown when editing existing company -->
        <VaSwitch v-if="props.company" v-model="newCompany.isActive" label="Active Status" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.subscriptionPlan" label="Subscription Plan (Optional)" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.subscriptionStartDate" label="Subscription Start Date (Optional)" type="date" :readonly="props.readonly"/>
        <VaInput v-model="newCompany.subscriptionEndDate" label="Subscription End Date (Optional)" type="date" :readonly="props.readonly"/>
        <div v-if="!props.readonly" class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
        <div v-else class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Close</VaButton>
        </div>
    </VaForm>
</template>
