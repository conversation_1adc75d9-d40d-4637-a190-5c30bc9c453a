import { computed, ref, Ref, unref } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/companies';
import { Company } from '../types';
import { useCompaniesStore } from '../../../stores/companyStores';

const makePaginationRef = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 });
const makeSortingRef = () => ref<Sorting>({ sortBy: 'createdAt', sortingOrder: 'DESC' });

export const useCompanies = (options?: { sorting?: Ref<Sorting>; pagination?: Ref<Pagination> }) => {
    const isLoading = ref(false);
    const companiesStore = useCompaniesStore();

    const { sorting = makeSortingRef(), pagination = makePaginationRef() } = options ?? {};

    const fetch = async () => {
        isLoading.value = true;
        await companiesStore.getAll({
            sorting: unref(sorting),
            pagination: unref(pagination),
        });
        pagination.value = companiesStore.pagination;
        isLoading.value = false;
    };

    const companies = computed(() => {
        const paginated = companiesStore.items.slice(
            (pagination.value.page - 1) * pagination.value.perPage,
            pagination.value.page * pagination.value.perPage,
        );

        const getSortItem = (obj: any, sortBy: Sorting['sortBy']) => {
            if (sortBy === 'name') {
                return obj.name;
            }

            if (sortBy === 'updatedAt') {
                 return new Date(obj[sortBy]);
            }

            if (sortBy === 'createdAt') {
                return new Date(obj[sortBy]);
            }

            return obj[sortBy];
        };

        if (sorting.value.sortBy && sorting.value.sortingOrder) {
            paginated.sort((a, b) => {
                a = getSortItem(a, sorting.value.sortBy!);
                b = getSortItem(b, sorting.value.sortBy!);

                if (a < b) {
                    return sorting.value.sortingOrder === 'asc' ? -1 : 1;
                }
                if (a > b) {
                    return sorting.value.sortingOrder === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }

        return paginated;
    });

    fetch();

    return {
        isLoading,
        companies,
        fetch,
        async add(company: Omit<Company, 'id' | 'createdAt' | 'updatedAt'>) {
            isLoading.value = true;
            await companiesStore.add(company);
            await fetch();
            isLoading.value = false;
        },

        async update(company: Company) {
            isLoading.value = true;
            await companiesStore.update(company);
            await fetch();
            isLoading.value = false;
        },

        async remove(company: Company) {
            isLoading.value = true;
            await companiesStore.remove(company);
            await fetch();
            isLoading.value = false;
        },
        pagination,
        sorting,
    };
};
