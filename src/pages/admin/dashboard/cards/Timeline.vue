<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VaTimelineItem from '../../../../components/va-timeline-item.vue'
import { getRecentAuditLogs, formatRelativeTime, generateTimelineDescription } from '../../../../data/pages/audit'
import type { AuditTrail } from '../../../../pages/audit/types'

// Reactive data
const auditLogs = ref<AuditTrail[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

// Load recent audit logs
const loadRecentLogs = async () => {
  try {
    loading.value = true
    error.value = null
    const logs = await getRecentAuditLogs(7) // Get last 7 logs for timeline
    auditLogs.value = logs
  } catch (err) {
    console.error('Failed to load recent audit logs:', err)
    error.value = 'Failed to load recent activity'
    auditLogs.value = []
  } finally {
    loading.value = false
  }
}

// Load data on component mount
onMounted(() => {
  loadRecentLogs()
})
</script>

<template>
    <VaCard>
        <VaCardTitle class="flex justify-between">
            <h1 class="card-title text-secondary font-bold uppercase">Recent Activity</h1>
            <VaButton
              preset="secondary"
              size="small"
              icon="refresh"
              @click="loadRecentLogs"
              :loading="loading"
            />
        </VaCardTitle>
        <VaCardContent>
            <!-- Loading State -->
            <div v-if="loading" class="text-center py-8">
                <VaProgressCircle indeterminate size="small" />
                <p class="text-secondary mt-2">Loading recent activity...</p>
            </div>

            <!-- Error State -->
            <div v-else-if="error" class="text-center py-8">
                <VaIcon name="error_outline" size="large" color="danger" />
                <p class="text-danger mt-2">{{ error }}</p>
                <VaButton
                  size="small"
                  preset="secondary"
                  @click="loadRecentLogs"
                  class="mt-2"
                >
                  Retry
                </VaButton>
            </div>

            <!-- Empty State -->
            <div v-else-if="auditLogs.length === 0" class="text-center py-8">
                <VaIcon name="history" size="large" color="secondary" />
                <p class="text-secondary mt-2">No recent activity</p>
            </div>

            <!-- Timeline Data -->
            <table v-else class="mt-4">
                <tbody>
                <VaTimelineItem
                  v-for="log in auditLogs"
                  :key="log.id"
                  :date="formatRelativeTime(log.createdAt)"
                >
                    <span v-html="generateTimelineDescription(log)"></span>
                </VaTimelineItem>
                </tbody>
            </table>

            <!-- View All Link -->
            <div v-if="auditLogs.length > 0" class="mt-4 text-center">
                <RouterLink
                  to="/audit-logs"
                  class="va-link text-sm"
                >
                  View all audit logs →
                </RouterLink>
            </div>
        </VaCardContent>
    </VaCard>
</template>
