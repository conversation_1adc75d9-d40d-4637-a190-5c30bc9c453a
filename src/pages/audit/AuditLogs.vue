<template>
  <div class="audit-logs">
    <VaCard>
      <VaCardTitle class="flex justify-between items-center">
        <h1 class="card-title text-secondary font-bold">Audit Logs</h1>
        <div class="flex gap-2">
          <VaButton
            preset="secondary"
            size="small"
            icon="refresh"
            @click="loadAuditLogs"
            :loading="loading"
          >
            Refresh
          </VaButton>
          <VaButton
            preset="primary"
            size="small"
            icon="download"
            @click="exportAuditLogs"
          >
            Export
          </VaButton>
        </div>
      </VaCardTitle>

      <VaCardContent>
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <VaSelect
            v-model="filters.module"
            :options="moduleOptions"
            placeholder="Filter by module"
            clearable
            @update:model-value="applyFilters"
          />
          <VaSelect
            v-model="filters.action"
            :options="actionOptions"
            placeholder="Filter by action"
            clearable
            @update:model-value="applyFilters"
          />
          <VaInput
            v-model="filters.username"
            placeholder="Filter by username"
            clearable
            @update:model-value="debouncedApplyFilters"
          />
          <VaInput
            v-model="filters.search"
            placeholder="Search description..."
            clearable
            @update:model-value="debouncedApplyFilters"
          />
        </div>

        <!-- Date Range Filter -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <VaDateInput
            v-model="filters.startDate"
            placeholder="Start date"
            clearable
            @update:model-value="applyFilters"
          />
          <VaDateInput
            v-model="filters.endDate"
            placeholder="End date"
            clearable
            @update:model-value="applyFilters"
          />
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-8">
          <VaProgressCircle indeterminate />
          <span class="ml-2">Loading audit logs...</span>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-8">
          <VaIcon name="error_outline" size="large" color="danger" />
          <p class="text-danger mt-2">{{ error }}</p>
          <VaButton 
            size="small" 
            preset="secondary" 
            @click="loadAuditLogs"
            class="mt-2"
          >
            Retry
          </VaButton>
        </div>

        <!-- Data Table -->
        <VaDataTable
          v-else
          :items="auditLogs"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          @update:pagination="updatePagination"
          striped
          hoverable
        >
          <template #cell(action)="{ rowData }">
            <VaBadge
              :text="getActionDisplayName(rowData.action)"
              :color="getActionColor(rowData.action)"
              size="small"
            />
          </template>

          <template #cell(module)="{ rowData }">
            <VaBadge
              :text="getModuleDisplayName(rowData.module)"
              color="info"
              size="small"
              outline
            />
          </template>

          <template #cell(user)="{ rowData }">
            <div class="flex items-center">
              <VaAvatar
                :src="rowData.user?.avatar"
                :fallback-text="rowData.userFullName || rowData.username || 'S'"
                size="small"
                class="mr-2"
              />
              <span>{{ rowData.userFullName || rowData.username || 'System' }}</span>
            </div>
          </template>

          <template #cell(description)="{ rowData }">
            <div class="max-w-md">
              <p class="truncate" :title="rowData.description">
                {{ rowData.description || generateTimelineDescription(rowData) }}
              </p>
            </div>
          </template>

          <template #cell(createdAt)="{ rowData }">
            <div>
              <div class="text-sm">{{ formatFullDate(rowData.createdAt) }}</div>
              <div class="text-xs text-secondary">{{ formatRelativeTime(rowData.createdAt) }}</div>
            </div>
          </template>

          <template #cell(actions)="{ rowData }">
            <VaButton
              preset="secondary"
              size="small"
              icon="visibility"
              @click="viewDetails(rowData)"
            />
          </template>
        </VaDataTable>
      </VaCardContent>
    </VaCard>

    <!-- Details Modal -->
    <VaModal
      v-model="showDetailsModal"
      title="Audit Log Details"
      size="large"
      max-width="800px"
    >
      <div v-if="selectedLog" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-semibold text-secondary">Action</label>
            <VaBadge
              :text="getActionDisplayName(selectedLog.action)"
              :color="getActionColor(selectedLog.action)"
              class="mt-1"
            />
          </div>
          <div>
            <label class="text-sm font-semibold text-secondary">Module</label>
            <VaBadge
              :text="getModuleDisplayName(selectedLog.module)"
              color="info"
              outline
              class="mt-1"
            />
          </div>
        </div>

        <div v-if="selectedLog.userFullName || selectedLog.username">
          <label class="text-sm font-semibold text-secondary">User</label>
          <p class="mt-1">{{ selectedLog.userFullName || selectedLog.username }}</p>
        </div>

        <div v-if="selectedLog.description">
          <label class="text-sm font-semibold text-secondary">Description</label>
          <p class="mt-1">{{ selectedLog.description }}</p>
        </div>

        <div v-if="selectedLog.entityId">
          <label class="text-sm font-semibold text-secondary">Entity ID</label>
          <p class="mt-1 font-mono text-sm">{{ selectedLog.entityId }}</p>
        </div>

        <div v-if="selectedLog.ipAddress">
          <label class="text-sm font-semibold text-secondary">IP Address</label>
          <p class="mt-1 font-mono text-sm">{{ selectedLog.ipAddress }}</p>
        </div>

        <div>
          <label class="text-sm font-semibold text-secondary">Timestamp</label>
          <p class="mt-1">{{ formatFullDate(selectedLog.createdAt) }}</p>
        </div>

        <div v-if="selectedLog.oldValues && Object.keys(selectedLog.oldValues).length > 0">
          <label class="text-sm font-semibold text-secondary">Previous Values</label>
          <VaCodeBlock
            :code="JSON.stringify(selectedLog.oldValues, null, 2)"
            language="json"
            class="mt-1"
          />
        </div>

        <div v-if="selectedLog.newValues && Object.keys(selectedLog.newValues).length > 0">
          <label class="text-sm font-semibold text-secondary">New Values</label>
          <VaCodeBlock
            :code="JSON.stringify(selectedLog.newValues, null, 2)"
            language="json"
            class="mt-1"
          />
        </div>
      </div>
    </VaModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { debounce } from 'lodash-es'
import { 
  getAuditLogs, 
  formatRelativeTime, 
  formatFullDate, 
  generateTimelineDescription 
} from '../../data/pages/audit'
import { 
  getActionDisplayName, 
  getModuleDisplayName, 
  getActionColor,
  AuditAction,
  AuditModule
} from './types'
import type { AuditTrail, AuditQueryParams, PaginatedAuditResult } from './types'

// Reactive data
const auditLogs = ref<AuditTrail[]>([])
const loading = ref(true)
const error = ref<string | null>(null)
const showDetailsModal = ref(false)
const selectedLog = ref<AuditTrail | null>(null)

// Filters
const filters = ref<AuditQueryParams>({
  page: 1,
  limit: 20,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// Pagination
const pagination = ref({
  page: 1,
  perPage: 20,
  total: 0
})

// Table columns
const columns = [
  { key: 'action', label: 'Action', sortable: true },
  { key: 'module', label: 'Module', sortable: true },
  { key: 'user', label: 'User', sortable: false },
  { key: 'description', label: 'Description', sortable: false },
  { key: 'createdAt', label: 'Date', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false, width: '80px' }
]

// Filter options
const moduleOptions = computed(() => 
  Object.values(AuditModule).map(module => ({
    text: getModuleDisplayName(module),
    value: module
  }))
)

const actionOptions = computed(() => 
  Object.values(AuditAction).map(action => ({
    text: getActionDisplayName(action),
    value: action
  }))
)

// Methods
const loadAuditLogs = async () => {
  try {
    loading.value = true
    error.value = null
    
    const result: PaginatedAuditResult = await getAuditLogs(filters.value)
    auditLogs.value = result.data
    pagination.value = {
      page: result.page,
      perPage: result.limit,
      total: result.total
    }
  } catch (err) {
    console.error('Failed to load audit logs:', err)
    error.value = 'Failed to load audit logs'
    auditLogs.value = []
  } finally {
    loading.value = false
  }
}

const applyFilters = () => {
  filters.value.page = 1
  pagination.value.page = 1
  loadAuditLogs()
}

const debouncedApplyFilters = debounce(applyFilters, 500)

const updatePagination = (newPagination: any) => {
  filters.value.page = newPagination.page
  filters.value.limit = newPagination.perPage
  pagination.value = newPagination
  loadAuditLogs()
}

const viewDetails = (log: AuditTrail) => {
  selectedLog.value = log
  showDetailsModal.value = true
}

const exportAuditLogs = () => {
  // TODO: Implement export functionality
  console.log('Export audit logs')
}

// Lifecycle
onMounted(() => {
  loadAuditLogs()
})
</script>

<style scoped>
.audit-logs {
  padding: 1rem;
}
</style>
