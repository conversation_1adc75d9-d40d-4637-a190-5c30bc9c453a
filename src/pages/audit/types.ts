export type UUID = `${string}-${string}-${string}-${string}-${string}`

export enum AuditAction {
    CREATE = 'CREATE',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',
    LOGIN = 'LOGIN',
    LOGOUT = 'LOGOUT',
    VIEW = 'VIEW',
    EXPORT = 'EXPORT',
    IMPORT = 'IMPORT',
    ACTIVATE = 'ACTIVATE',
    DEACTIVATE = 'DEACTIVATE',
    APPROVE = 'APPROVE',
    REJECT = 'REJECT',
    SCHEDULE = 'SCHEDULE',
    COMPLETE = 'COMPLETE',
    CANCEL = 'CANCEL'
}

export enum AuditModule {
    USER = 'USER',
    CLIENT = 'CLIENT',
    COMPANY = 'COMPANY',
    TENANT = 'TENANT',
    VALUATION = 'VALUATION',
    VEHICLE = 'VEHICLE',
    VEHICLE_MAKE = 'VEHICLE_MAKE',
    VEHICLE_MODEL = 'VEHICLE_MODEL',
    VEHICLE_TYPE = 'VEHICLE_TYPE',
    VEHICLE_BODY_TYPE = 'VEHICLE_BODY_TYPE',
    VEHICLE_FUEL_TYPE = 'VEHICLE_FUEL_TYPE',
    VEHICLE_TRANSMISSION = 'VEHICLE_TRANSMISSION',
    VEHICLE_LIGHTING = 'VEHICLE_LIGHTING',
    ROLE = 'ROLE',
    PERMISSION = 'PERMISSION',
    AUTH = 'AUTH',
    SYSTEM = 'SYSTEM'
}

export type User = {
    id: UUID
    username: string
    email: string
    firstName: string
    lastName: string
    phone: string
    isActive: boolean
    createdAt: string
    updatedAt: string
}

export type AuditTrail = {
    id: UUID
    module: AuditModule
    action: AuditAction
    entityId?: string
    entityName?: string
    description?: string
    oldValues?: Record<string, any>
    newValues?: Record<string, any>
    ipAddress?: string
    userAgent?: string
    metadata?: string
    userId?: string
    username?: string
    userFullName?: string
    user?: User
    createdAt: string
    updatedAt: string
}

export type AuditQueryParams = {
    page?: number
    limit?: number
    module?: AuditModule
    action?: AuditAction
    userId?: string
    username?: string
    entityId?: string
    entityName?: string
    startDate?: string
    endDate?: string
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    search?: string
}

export type PaginatedAuditResult = {
    data: AuditTrail[]
    total: number
    page: number
    limit: number
    totalPages: number
}

export type AuditStatistics = {
    totalLogs: number
    moduleStats: Array<{
        module: string
        count: number
    }>
    actionStats: Array<{
        action: string
        count: number
    }>
    topUsers: Array<{
        username: string
        count: number
    }>
    period: string
}

// Helper functions for display
export const getActionDisplayName = (action: AuditAction): string => {
    const actionMap = {
        [AuditAction.CREATE]: 'Created',
        [AuditAction.UPDATE]: 'Updated',
        [AuditAction.DELETE]: 'Deleted',
        [AuditAction.VIEW]: 'Viewed',
        [AuditAction.LOGIN]: 'Logged in',
        [AuditAction.LOGOUT]: 'Logged out',
        [AuditAction.ACTIVATE]: 'Activated',
        [AuditAction.DEACTIVATE]: 'Deactivated',
        [AuditAction.APPROVE]: 'Approved',
        [AuditAction.REJECT]: 'Rejected',
        [AuditAction.SCHEDULE]: 'Scheduled',
        [AuditAction.COMPLETE]: 'Completed',
        [AuditAction.CANCEL]: 'Cancelled',
        [AuditAction.EXPORT]: 'Exported',
        [AuditAction.IMPORT]: 'Imported',
    }
    return actionMap[action] || action
}

export const getModuleDisplayName = (module: AuditModule): string => {
    const moduleMap = {
        [AuditModule.USER]: 'User',
        [AuditModule.CLIENT]: 'Client',
        [AuditModule.COMPANY]: 'Company',
        [AuditModule.TENANT]: 'Tenant',
        [AuditModule.VALUATION]: 'Valuation',
        [AuditModule.VEHICLE]: 'Vehicle',
        [AuditModule.VEHICLE_MAKE]: 'Vehicle Make',
        [AuditModule.VEHICLE_MODEL]: 'Vehicle Model',
        [AuditModule.VEHICLE_TYPE]: 'Vehicle Type',
        [AuditModule.VEHICLE_BODY_TYPE]: 'Vehicle Body Type',
        [AuditModule.VEHICLE_FUEL_TYPE]: 'Vehicle Fuel Type',
        [AuditModule.VEHICLE_TRANSMISSION]: 'Vehicle Transmission',
        [AuditModule.VEHICLE_LIGHTING]: 'Vehicle Lighting',
        [AuditModule.ROLE]: 'Role',
        [AuditModule.PERMISSION]: 'Permission',
        [AuditModule.AUTH]: 'Authentication',
        [AuditModule.SYSTEM]: 'System',
    }
    return moduleMap[module] || module
}

export const getActionColor = (action: AuditAction): string => {
    const colorMap = {
        [AuditAction.CREATE]: 'success',
        [AuditAction.UPDATE]: 'warning',
        [AuditAction.DELETE]: 'danger',
        [AuditAction.VIEW]: 'info',
        [AuditAction.LOGIN]: 'primary',
        [AuditAction.LOGOUT]: 'secondary',
        [AuditAction.ACTIVATE]: 'success',
        [AuditAction.DEACTIVATE]: 'warning',
        [AuditAction.APPROVE]: 'success',
        [AuditAction.REJECT]: 'danger',
        [AuditAction.SCHEDULE]: 'info',
        [AuditAction.COMPLETE]: 'success',
        [AuditAction.CANCEL]: 'warning',
        [AuditAction.EXPORT]: 'info',
        [AuditAction.IMPORT]: 'info',
    }
    return colorMap[action] || 'secondary'
}
