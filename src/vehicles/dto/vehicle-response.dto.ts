import {Expose, Type} from "class-transformer";
import {ApiProperty} from "@nestjs/swagger";
import {VehicleTypeResponseDto} from "../../vehicle-type/dto/vehicle-type-response.dto";
import {VehicleBodyTypeResponseDto} from "../../vehicle-body-type/dto/vehicle-body-type-response.dto";
import {VehicleMakeResponseDto} from "../../vehicle-make/dto/vehicle-make-response.dto";
import {VehicleModelResponseDto} from "../../vehicle-model/dto/vehicle-model-response.dto";
import {VehicleLightingResponseDto} from "../../vehicle-lighting/dto/vehicle-lighting-response.dto";
import {VehicleFuelTypeResponseDto} from "../../vehicle-fuel-type/dto/vehicle-fuel-type-response.dto";
import {VehicleTransmissionResponseDto} from "../../vehicle-transmission/dto/vehicle-transmission-response.dto";

export class VehicleResponseDto {
    @Expose()
    @ApiProperty({description: 'Unique identifier'})
    id: string;

    @Expose()
    @ApiProperty({description: 'License plate number'})
    licensePlate: string;

    @Expose()
    @ApiProperty({description: 'Vehicle identification number'})
    vin: string;

    @Expose()
    @ApiProperty({description: 'Engine number'})
    engineNo: string;

    @Expose()
    @ApiProperty({description: 'Vehicle mileage'})
    mileage: string;  // Fixed typo from "millage" to "mileage"

    @Expose()
    @ApiProperty({description: 'Engine capacity'})
    engineCapacity: string;

    @Expose()
    @ApiProperty({description: 'Vehicle color'})
    color: string;

    @Expose()
    @ApiProperty({description: 'Year of manufacture'})
    yearOfMan: string;

    @Expose()
    @ApiProperty({description: 'Local registration date'})
    localRegDate: string;

    @Expose()
    @ApiProperty({description: 'Country of origin code'})
    countryOfOrigin: number;

    @Expose()
    @ApiProperty({description: 'Vehicle type ID'})
    typeId: string;

    @Expose()
    @Type(() => VehicleTypeResponseDto)
    @ApiProperty({description: 'Vehicle type details', type: () => VehicleTypeResponseDto})
    vehicleType: VehicleTypeResponseDto;

    @Expose()
    @ApiProperty({description: 'Body type ID'})
    bodyTypeId: string;

    @Expose()
    @Type(() => VehicleBodyTypeResponseDto)
    @ApiProperty({description: 'Vehicle body type details', type: () => VehicleBodyTypeResponseDto})
    vehicleBodyType: VehicleBodyTypeResponseDto;

    @Expose()
    @ApiProperty({description: 'Make ID'})
    makeId: string;

    @Expose()
    @Type(() => VehicleMakeResponseDto)
    @ApiProperty({description: 'Vehicle make details', type: () => VehicleMakeResponseDto})
    vehicleMake: VehicleMakeResponseDto;

    @Expose()
    @ApiProperty({description: 'Model ID'})
    modelId: string;

    @Expose()
    @Type(() => VehicleModelResponseDto)
    @ApiProperty({description: 'Vehicle model details', type: () => VehicleModelResponseDto})
    vehicleModel: VehicleModelResponseDto;

    @Expose()
    @ApiProperty({description: 'Lighting ID'})
    lightingId: string;

    @Expose()
    @Type(() => VehicleLightingResponseDto)
    @ApiProperty({description: 'Vehicle lighting details', type: () => VehicleLightingResponseDto})
    vehicleLighting: VehicleLightingResponseDto;

    @Expose()
    @ApiProperty({description: 'Fuel type ID'})
    fuelTypeId: string;

    @Expose()
    @Type(() => VehicleFuelTypeResponseDto)
    @ApiProperty({description: 'Vehicle fuel type details', type: () => VehicleFuelTypeResponseDto})
    vehicleFuelType: VehicleFuelTypeResponseDto;

    @Expose()
    @ApiProperty({description: 'Transmission ID'})
    transmissionId: string;

    @Expose()
    @Type(() => VehicleTransmissionResponseDto)
    @ApiProperty({description: 'Vehicle transmission details', type: () => VehicleTransmissionResponseDto})
    vehicleTransmission: VehicleTransmissionResponseDto;

    @Expose()
    @ApiProperty({description: 'Creation timestamp'})
    createdAt: Date;

    @Expose()
    @ApiProperty({description: 'Last update timestamp'})
    updatedAt: Date;

    @Expose()
    @ApiProperty({description: 'Deletion timestamp', required: false})
    deletedAt?: Date;
}