import {ApiProperty} from '@nestjs/swagger';
import {IsNotEmpty, IsNumber, IsString, IsUUID, MinLength} from 'class-validator';

export class CreateVehicleDto {
    @ApiProperty({description: 'The license plate number', example: 'ABC123'})
    @IsString()
    @IsNotEmpty()
    licensePlate: string;

    @ApiProperty({description: 'The vehicle identification number', example: '1HGCM82633A123456'})
    @IsString()
    @IsNotEmpty()
    @MinLength(17)
    vin: string;

    @ApiProperty({description: 'The engine number', example: '**********'})
    @IsString()
    @IsNotEmpty()
    engineNo: string;

    @ApiProperty({description: 'The vehicle mileage', example: '50000'})
    @IsString()
    @IsNotEmpty()
    mileage: string;

    @ApiProperty({description: 'The engine capacity', example: '2000cc'})
    @IsString()
    @IsNotEmpty()
    engineCapacity: string;

    @ApiProperty({description: 'The vehicle color', example: 'Blue'})
    @IsString()
    @IsNotEmpty()
    color: string;

    @ApiProperty({description: 'Year of manufacture', example: '2020'})
    @IsString()
    @IsNotEmpty()
    yearOfMan: string;

    @ApiProperty({description: 'Local registration date', example: '2021-01-01'})
    @IsString()
    @IsNotEmpty()
    localRegDate: string;

    @ApiProperty({description: 'Country of origin code', example: 392})
    @IsNumber()
    @IsNotEmpty()
    countryOfOrigin: number;

    @ApiProperty({description: 'Vehicle type ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    typeId: string;

    @ApiProperty({description: 'Vehicle body type ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    bodyTypeId: string;

    @ApiProperty({description: 'Vehicle make ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    makeId: string;

    @ApiProperty({description: 'Vehicle model ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    modelId: string;

    @ApiProperty({description: 'Vehicle lighting ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    lightingId: string;

    @ApiProperty({description: 'Vehicle fuel type ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    fuelTypeId: string;

    @ApiProperty({description: 'Vehicle transmission ID', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'})
    @IsUUID()
    @IsNotEmpty()
    transmissionId: string;
}