import {ApiProperty} from '@nestjs/swagger';
import {Column, Enti<PERSON>, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany} from "typeorm";
import {BaseEntity} from "../../database/entities/base.entity";
import {VehicleMake} from "../../vehicle-make/entities/vehicle-make.entity";
import {VehicleType} from "../../vehicle-type/entities/vehicle-type.entity";
import {VehicleBodyType} from "../../vehicle-body-type/entities/vehicle-body-type.entity";
import {VehicleModel} from "../../vehicle-model/entities/vehicle-model.entity";
import {VehicleLighting} from "../../vehicle-lighting/entities/vehicle-lighting.entity";
import {VehicleFuelType} from "../../vehicle-fuel-type/entities/vehicle-fuel-type.entity";
import {VehicleTransmission} from "../../vehicle-transmission/entities/vehicle-transmission.entity";
import {Valuation} from "../../valuations/entities/valuation.entity";

@Entity('vehicle')
export class Vehicle extends BaseEntity {
    @Column()
    @ApiProperty({description: 'The license plate number'})
    licensePlate: string;

    @Column()
    @ApiProperty({description: 'The vehicle identification number'})
    vin: string;

    @Column()
    @ApiProperty({description: 'The vehicle engine number'})
    engineNo: string;

    @Column()
    @ApiProperty({description: 'The vehicle mileage'})
    mileage: string;

    @Column()
    @ApiProperty({description: 'The vehicle engine capacity'})
    engineCapacity: string;

    @Column()
    @ApiProperty({description: 'The vehicle color'})
    color: string;

    @Column()
    @ApiProperty({description: 'The vehicle year of manufacture'})
    yearOfMan: string;

    @Column()
    @ApiProperty({description: 'The vehicles local year of registration'})
    localRegDate: string;

    @Column()
    @ApiProperty({description: 'The country of origin of the vehicle'})
    countryOfOrigin: number;

    @Column()
    @ApiProperty({description: 'The type of the vehicle'})
    typeId: string;

    @ManyToOne(() => VehicleType, (type) => type.vehicles, {eager: true})
    @JoinColumn({name: 'typeId'})
    @ApiProperty({description: 'The vehicle type associated with the vehicle model', type: () => VehicleType})
    vehicleType: VehicleType;

    @Column()
    @ApiProperty({description: 'The body type of the vehicle'})
    bodyTypeId: string;

    @ManyToOne(() => VehicleBodyType, (bodyType) => bodyType.vehicles, {eager: true})
    @JoinColumn({name: 'bodyTypeId'})
    @ApiProperty({description: 'The vehicle body type associated with the vehicle model', type: () => VehicleBodyType})
    vehicleBodyType: VehicleBodyType;

    @Column()
    @ApiProperty({description: 'The make of the vehicle'})
    makeId: string;

    @ManyToOne(() => VehicleMake, (make) => make.vehicles, {eager: true})
    @JoinColumn({name: 'makeId'})
    @ApiProperty({description: 'The make associated with the vehicle model', type: () => VehicleMake})
    vehicleMake: VehicleMake;

    @Column()
    @ApiProperty({description: 'The model of the vehicle'})
    modelId: string;

    @ManyToOne(() => VehicleModel, (model) => model.vehicles, {eager: true})
    @JoinColumn({name: 'modelId'})
    @ApiProperty({description: 'The model associated with the vehicle', type: () => VehicleModel})
    vehicleModel: VehicleModel;

    @Column()
    @ApiProperty({description: 'The lighting of the vehicle'})
    lightingId: string;

    @ManyToOne(() => VehicleLighting, (lighting) => lighting.vehicles, {eager: true})
    @JoinColumn({name: 'lightingId'})
    @ApiProperty({description: 'The lighting associated with the vehicle', type: () => VehicleLighting})
    vehicleLighting: VehicleLighting;

    @Column()
    @ApiProperty({description: 'The fuel type of the vehicle'})
    fuelTypeId: string;

    @ManyToOne(() => VehicleFuelType, (fuelType) => fuelType.vehicles, {eager: true})
    @JoinColumn({name: 'fuelTypeId'})
    @ApiProperty({description: 'The lighting associated with the vehicle', type: () => VehicleFuelType})
    vehicleFuelType: VehicleFuelType;

    @Column()
    @ApiProperty({description: 'The transmission of the vehicle'})
    transmissionId: string;

    @ManyToOne(() => VehicleTransmission, (transmission) => transmission.vehicles, {eager: true})
    @JoinColumn({name: 'transmissionId'})
    @ApiProperty({description: 'The transmission associated with the vehicle', type: () => VehicleTransmission})
    vehicleTransmission: VehicleTransmission;

    @OneToMany(() => Valuation, (valuation) => valuation.vehicle)
    valuations: Valuation[];

}
