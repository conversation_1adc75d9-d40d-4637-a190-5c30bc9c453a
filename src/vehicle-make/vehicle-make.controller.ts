import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleMakeService} from './vehicle-make.service';
import {CreateVehicleMakeDto} from './dto/create-vehicle-make.dto';
import {UpdateVehicleMakeDto} from './dto/update-vehicle-make.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleMake} from "./entities/vehicle-make.entity";

@ApiTags('Vehicle Make')
@Controller('vehicle-make')
export class VehicleMakeController {
    constructor(private readonly vehicleMakeService: VehicleMakeService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle make'})
    @ApiResponse({status: 201, description: 'The vehicle make has been successfully created.', type: VehicleMake})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    // @ApiResponse({status: 404, description: 'Vehicle make not found.'})
    @ApiResponse({status: 409, description: 'Vehicle make already exists.'})
    create(@Body() createVehicleMakeDto: CreateVehicleMakeDto) {
        return this.vehicleMakeService.create(createVehicleMakeDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Make'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle make retrieved successfully.',
        type: VehicleMake,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle make with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleMakeService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle make by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle make ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle make retrieved successfully.',
        type: VehicleMake,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle make with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleMakeService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle make'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle make ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle make successfully updated.',
        type: VehicleMake
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle make with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle make with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleMakeDto: UpdateVehicleMakeDto
    ) {
        return this.vehicleMakeService.update(identifier, updateVehicleMakeDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle make'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle make ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle make successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle make with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleMakeService.remove(id);
    }

}
