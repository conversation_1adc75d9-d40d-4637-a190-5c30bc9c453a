import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {VehicleModel} from "../../vehicle-model/entities/vehicle-model.entity";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_make')
export class VehicleMake extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle make'})
    name: string;

    @OneToMany(() => VehicleModel, (model) => model.vehicleMake)
    models: VehicleModel[];

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleMake)
    vehicles: Vehicle[];
}
