import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateVehicleMakeDto} from './dto/create-vehicle-make.dto';
import {VehicleMakeResponseDto} from "./dto/vehicle-make-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleMake} from "./entities/vehicle-make.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleMakeDto} from "./dto/update-vehicle-make.dto";
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class VehicleMakeService {
    private readonly logger = new Logger(VehicleMakeService.name);

    constructor(
        @InjectRepository(VehicleMake) private readonly vehicleMakeRepository: Repository<VehicleMake>,
    ) {
    }

    async create(createVehicleMakeDto: CreateVehicleMakeDto): Promise<{
        data: VehicleMakeResponseDto;
        message: string
    }> {
        const existingVehicleMake = await this.vehicleMakeRepository.findOne({
            where: {name: createVehicleMakeDto.name},
        });

        if (existingVehicleMake) {
            throw new ConflictException('Vehicle make with this name already exists');
        }

        const vehicleMake = this.vehicleMakeRepository.create({...createVehicleMakeDto});
        const savedVehicleMake = await this.vehicleMakeRepository.save(vehicleMake);

        return {
            data: plainToInstance(VehicleMakeResponseDto, savedVehicleMake, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle make successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleMakeResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleMakeRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleMakeResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle makes retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleMakeResponseDto;
        message: string;
    }> {
        let vehicleMake: VehicleMake | null = null;

        if (isUUID(query)) {
            vehicleMake = await this.vehicleMakeRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleMake) {
            vehicleMake = await this.vehicleMakeRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleMake) {
            throw new NotFoundException(
                `Vehicle make with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleMakeResponseDto, vehicleMake, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle make retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleMakeDto: UpdateVehicleMakeDto,
    ): Promise<{ data: VehicleMakeResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleMake = await this.vehicleMakeRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleMake) {
            throw new NotFoundException('Vehicle make with ID or name not found.');
        }

        if (
            updateVehicleMakeDto.name &&
            updateVehicleMakeDto.name !== vehicleMake.name
        ) {
            const existing = await this.vehicleMakeRepository.findOne({
                where: {name: updateVehicleMakeDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle make with the provided name already exists.');
            }
        }

        Object.assign(vehicleMake, updateVehicleMakeDto);
        const updated = await this.vehicleMakeRepository.save(vehicleMake);

        const data = plainToInstance(VehicleMakeResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle make successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleMake = await this.vehicleMakeRepository.findOne({where: {id}});

        if (!vehicleMake) {
            throw new NotFoundException(`Vehicle make with id ${id} not found`);
        }

        await this.vehicleMakeRepository.remove(vehicleMake);

        return {message: 'The vehicle make successfully deleted.'};
    }
}
