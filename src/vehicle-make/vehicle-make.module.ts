import {Module} from '@nestjs/common';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleMakeService} from './vehicle-make.service';
import {VehicleMakeController} from './vehicle-make.controller';
import {VehicleMake} from "./entities/vehicle-make.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleMake])
    ],
    controllers: [VehicleMakeController],
    providers: [VehicleMakeService],
    exports: [VehicleMakeService]
})

export class VehicleMakeModule {
}
