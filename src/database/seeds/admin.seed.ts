import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Role } from '../../users/entities/role.entity';
import { Permission } from '../../users/entities/permission.entity';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminSeeder {
    constructor(
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
        @InjectRepository(Permission)
        private readonly permissionRepository: Repository<Permission>,
        private readonly configService: ConfigService,
    ) {
    }

    async seed() {
        // Create admin role if it doesn't exist
        let adminRole = await this.roleRepository.findOne({ where: { name: 'Super Admin' } });
        if (!adminRole) {
            adminRole = this.roleRepository.create({
                name: 'Super Admin',
                description: 'Administrator role with full access',
            });
            adminRole = await this.roleRepository.save(adminRole);
        }

        // Create admin user if it doesn't exist
        const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
        const adminPassword = this.configService.get<string>('ADMIN_PASSWORD');
        const adminPhone = this.configService.get<string>('ADMIN_PHONE');

        if (!adminEmail || !adminPassword) {
            throw new Error('ADMIN_EMAIL and ADMIN_PASSWORD must be set in .env');
        }

        // Check for existing admin by email
        const existingAdmin = await this.userRepository.findOne({
            where: { email: adminEmail },
        });

        if (!existingAdmin) {
            // Generate a unique username based on email
            const username = `admin_${adminEmail.split('@')[0]}`;

            // Check if username exists
            const existingUsername = await this.userRepository.findOne({
                where: { username },
            });

            if (existingUsername) {
                throw new Error('Username already exists. Please use a different email address.');
            }

            const hashedPassword = await bcrypt.hash(adminPassword, 10);
            const adminUser = this.userRepository.create({
                username,
                email: adminEmail,
                password: hashedPassword,
                phone: adminPhone,
                firstName: 'Admin',
                lastName: 'User',
                isActive: true,
                roles: [adminRole],
            });

            await this.userRepository.save(adminUser);
            console.log('Admin user created successfully');
        } else {
            console.log('Admin user already exists');
        }
    }
} 