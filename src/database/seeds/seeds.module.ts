import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../users/entities/user.entity';
import { Role } from '../../users/entities/role.entity';
import { Permission } from '../../users/entities/permission.entity';
import { PermissionsSeeder } from './permissions.seed';
import { RolesSeeder } from './roles.seed';
import { AdminSeeder } from './admin.seed';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, Permission])
  ],
  providers: [RolesSeeder, PermissionsSeeder, AdminSeeder, ],
  exports: [RolesSeeder, PermissionsSeeder, AdminSeeder, ]
})
export class SeedsModule {} 