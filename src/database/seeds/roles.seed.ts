import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../../users/entities/role.entity';
import { Permission } from '../../users/entities/permission.entity';

@Injectable()
export class RolesSeeder {
    constructor(
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
        @InjectRepository(Permission)
        private readonly permissionRepository: Repository<Permission>,
    ) {}

    private readonly defaultRoles = [
        // User roles
        { name: 'Super Admin', description: 'Administrator role with full access.' },
        { name: 'System Admin', description: 'Administrator role with almost full access.' },
        { name: 'User', description: 'Standard user with limited access.' },
        { name: 'Guest', description: 'Standard user with limited access to read.' },
    ];

    async seed() {
        // Get all permissions
        const allPermissions = await this.permissionRepository.find();
        
        for (const role of this.defaultRoles) {
            let existingRole = await this.roleRepository.findOne({
                where: { name: role.name },
                relations: ['permissions'],
            });

            if (!existingRole) {
                existingRole = this.roleRepository.create(role);
                await this.roleRepository.save(existingRole);
                console.log(`Created role: ${role.name}`);
            } else {
                console.log(`Role already exists: ${role.name}`);
            }

            // Determine which permissions should be assigned based on role
            let permissionsToAssign: Permission[] = [];
            switch (role.name) {
                case 'Super Admin':
                    permissionsToAssign = allPermissions;
                    break;

                case 'System Admin':
                    permissionsToAssign = allPermissions.filter(
                        p => p.action !== 'delete'
                    );
                    break;

                case 'User':
                    permissionsToAssign = allPermissions.filter(
                        p =>  p.action === 'read' ||  p.action === 'create' ||  p.action === 'update' ||  p.action === 'soft_delete'
                    );
                    break;

                case 'Guest':
                permissionsToAssign = allPermissions.filter(
                    p =>  p.action === 'read'
                );
                break;
            }

            // Check if permissions are already correctly assigned
            const currentPermissionIds = existingRole.permissions?.map(p => p.id) || [];
            const targetPermissionIds = permissionsToAssign.map(p => p.id);
            
            const needsUpdate = 
                currentPermissionIds.length !== targetPermissionIds.length ||
                !targetPermissionIds.every(id => currentPermissionIds.includes(id));

            if (needsUpdate) {
                // Log what's changing
                const addedPermissions = targetPermissionIds.filter(id => !currentPermissionIds.includes(id));
                const removedPermissions = currentPermissionIds.filter(id => !targetPermissionIds.includes(id));
                
                if (addedPermissions.length > 0) {
                    console.log(`Adding ${addedPermissions.length} permissions to role: ${role.name}`);
                }
                if (removedPermissions.length > 0) {
                    console.log(`Removing ${removedPermissions.length} permissions from role: ${role.name}`);
                }

                // Update permissions
                existingRole.permissions = permissionsToAssign;
                await this.roleRepository.save(existingRole);
                console.log(`Updated permissions for role: ${role.name}`);
            } else {
                console.log(`Permissions already correctly assigned for role: ${role.name}`);
            }
        }
    }
} 