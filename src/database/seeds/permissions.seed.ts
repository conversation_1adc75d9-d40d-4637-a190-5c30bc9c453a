import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission } from '../../users/entities/permission.entity';

@Injectable()
export class PermissionsSeeder {
    constructor(
        @InjectRepository(Permission)
        private readonly permissionRepository: Repository<Permission>,
    ) {}

    private readonly defaultPermissions = [
        // User permissions
        { name: "read_user", description: "Permission to read users", module: "users", action: "read" },
        { name: "create_user", description: "Permission to create users", module: "users", action: "create" },
        { name: "update_user", description: "Permission to update users", module: "users", action: "update" },
        { name: "delete_user", description: "Permission to delete users", module: "users", action: "delete" },
        { name: "soft_delete_user", description: "Permission to soft delete users", module: "users", action: "soft_delete" },
        { name: "read_deleted_user", description: "Permission to read soft deleted users", module: "users", action: "read_deleted" },
        { name: "update_deleted_user", description: "Permission to update soft deleted users", module: "users", action: "update_deleted" },
        { name: "restore_user", description: "Permission to restore soft deleted users", module: "users", action: "restore" },
        
        // Role permissions
        { name: "read_role", description: "Permission to read roles", module: "roles", action: "read" },
        { name: "create_role", description: "Permission to create roles", module: "roles", action: "create" },
        { name: "update_role", description: "Permission to update roles", module: "roles", action: "update" },
        { name: "delete_role", description: "Permission to delete roles", module: "roles", action: "delete" },
        { name: "soft_delete_role", description: "Permission to soft delete roles", module: "roles", action: "soft_delete" },
        { name: "read_deleted_role", description: "Permission to read soft deleted roles", module: "roles", action: "read_deleted" },
        { name: "update_deleted_role", description: "Permission to update soft deleted roles", module: "roles", action: "update_deleted" },
        { name: "restore_role", description: "Permission to restore soft deleted roles", module: "roles", action: "restore" },
        
        // Permission management
        { name: "read_permission", description: "Permission to read permissions", module: "permissions", action: "read" },
        { name: "create_permission", description: "Permission to create permissions", module: "permissions", action: "create" },
        { name: "update_permission", description: "Permission to update permissions", module: "permissions", action: "update" },
        { name: "delete_permission", description: "Permission to delete permissions", module: "permissions", action: "delete" },
        { name: "soft_delete_permission", description: "Permission to soft delete permissions", module: "permissions", action: "soft_delete" },
        { name: "read_deleted_permission", description: "Permission to read soft deleted permissions", module: "permissions", action: "read_deleted" },
        { name: "update_deleted_permission", description: "Permission to update soft deleted permissions", module: "permissions", action: "update_deleted" },
        { name: "restore_permission", description: "Permission to restore soft deleted permissions", module: "permissions", action: "restore" },
        
        // Vehicle permissions
        { name: "read_vehicle", description: "Permission to read vehicles", module: "vehicles", action: "read" },
        { name: "create_vehicle", description: "Permission to create vehicles", module: "vehicles", action: "create" },
        { name: "update_vehicle", description: "Permission to update vehicles", module: "vehicles", action: "update" },
        { name: "delete_vehicle", description: "Permission to delete vehicles", module: "vehicles", action: "delete" },
        { name: "soft_delete_vehicle", description: "Permission to soft delete vehicles", module: "vehicles", action: "soft_delete" },
        { name: "read_deleted_vehicle", description: "Permission to read soft deleted vehicles", module: "vehicles", action: "read_deleted" },
        { name: "update_deleted_vehicle", description: "Permission to update soft deleted vehicles", module: "vehicles", action: "update_deleted" },
        { name: "restore_vehicle", description: "Permission to restore soft deleted vehicles", module: "vehicles", action: "restore" },
        
        // Task permissions
        { name: "read_task", description: "Permission to read tasks", module: "tasks", action: "read" },
        { name: "create_task", description: "Permission to create tasks", module: "tasks", action: "create" },
        { name: "update_task", description: "Permission to update tasks", module: "tasks", action: "update" },
        { name: "delete_task", description: "Permission to delete tasks", module: "tasks", action: "delete" },
        { name: "soft_delete_task", description: "Permission to soft delete tasks", module: "tasks", action: "soft_delete" },
        { name: "read_deleted_task", description: "Permission to read soft deleted tasks", module: "tasks", action: "read_deleted" },
        { name: "update_deleted_task", description: "Permission to update soft deleted tasks", module: "tasks", action: "update_deleted" },
        { name: "restore_task", description: "Permission to restore soft deleted tasks", module: "tasks", action: "restore" },
        
        // Valuation permissions
        { name: "read_valuation", description: "Permission to read valuations", module: "valuations", action: "read" },
        { name: "create_valuation", description: "Permission to create valuations", module: "valuations", action: "create" },
        { name: "update_valuation", description: "Permission to update valuations", module: "valuations", action: "update" },
        { name: "delete_valuation", description: "Permission to delete valuations", module: "valuations", action: "delete" },
        { name: "soft_delete_valuation", description: "Permission to soft delete valuations", module: "valuations", action: "soft_delete" },
        { name: "read_deleted_valuation", description: "Permission to read soft deleted valuations", module: "valuations", action: "read_deleted" },
        { name: "update_deleted_valuation", description: "Permission to update soft deleted valuations", module: "valuations", action: "update_deleted" },
        { name: "restore_valuation", description: "Permission to restore soft deleted valuations", module: "valuations", action: "restore" },
    ];

    async seed() {
        for (const permission of this.defaultPermissions) {
            const existingPermission = await this.permissionRepository.findOne({
                where: { name: permission.name },
            });

            if (!existingPermission) {
                const newPermission = this.permissionRepository.create(permission);
                await this.permissionRepository.save(newPermission);
                console.log(`Created permission: ${permission.name}`);
            } else {
                console.log(`Permission already exists: ${permission.name}`);
            }
        }
    }
} 