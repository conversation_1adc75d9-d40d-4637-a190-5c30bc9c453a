import { Module } from '@nestjs/common';
import { VehicleTransmissionService } from './vehicle-transmission.service';
import { VehicleTransmissionController } from './vehicle-transmission.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleMake} from "../vehicle-make/entities/vehicle-make.entity";
import {VehicleTransmission} from "./entities/vehicle-transmission.entity";
import {VehicleMakeService} from "../vehicle-make/vehicle-make.service";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleTransmission])
    ],
  controllers: [VehicleTransmissionController],
  providers: [VehicleTransmissionService],
    exports: [VehicleTransmissionService]
})
export class VehicleTransmissionModule {}
