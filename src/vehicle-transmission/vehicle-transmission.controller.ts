import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleTransmissionService} from './vehicle-transmission.service';
import {CreateVehicleTransmissionDto} from './dto/create-vehicle-transmission.dto';
import {UpdateVehicleTransmissionDto} from './dto/update-vehicle-transmission.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleTransmission} from "./entities/vehicle-transmission.entity";

@ApiTags('Vehicle Transmission')
@Controller('vehicle-transmission')
export class VehicleTransmissionController {
    constructor(private readonly vehicleTransmissionService: VehicleTransmissionService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle transmission'})
    @ApiResponse({status: 201, description: 'The vehicle transmission has been successfully created.', type: VehicleTransmission})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    // @ApiResponse({status: 404, description: 'Vehicle transmission not found.'})
    @ApiResponse({status: 409, description: 'Vehicle transmission already exists.'})
    create(@Body() createVehicleTransmissionDto: CreateVehicleTransmissionDto) {
        return this.vehicleTransmissionService.create(createVehicleTransmissionDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Transmission'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle transmission retrieved successfully.',
        type: VehicleTransmission,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle transmission with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleTransmissionService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle transmission by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle transmission ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle transmission retrieved successfully.',
        type: VehicleTransmission,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle transmission with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleTransmissionService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle transmission'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle transmission ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle transmission successfully updated.',
        type: VehicleTransmission
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle transmission with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle transmission with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleTransmissionDto: UpdateVehicleTransmissionDto
    ) {
        return this.vehicleTransmissionService.update(identifier, updateVehicleTransmissionDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle transmission'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle transmission ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle transmission successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle transmission with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleTransmissionService.remove(id);
    }

}
