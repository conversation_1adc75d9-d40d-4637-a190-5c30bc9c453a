import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleTransmissionDto} from './dto/create-vehicle-transmission.dto';
import {VehicleTransmissionResponseDto} from "./dto/vehicle-transmission-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleTransmission} from "./entities/vehicle-transmission.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleTransmissionDto} from "./dto/update-vehicle-transmission.dto";

@Injectable()
export class VehicleTransmissionService {
    constructor(
        @InjectRepository(VehicleTransmission) private readonly vehicleTransmissionRepository: Repository<VehicleTransmission>,
    ) {
    }

    async create(createVehicleTransmissionDto: CreateVehicleTransmissionDto): Promise<{
        data: VehicleTransmissionResponseDto;
        message: string
    }> {
        const existingVehicleTransmission = await this.vehicleTransmissionRepository.findOne({
            where: {name: createVehicleTransmissionDto.name},
        });

        if (existingVehicleTransmission) {
            throw new ConflictException('Vehicle transmission with this name already exists');
        }

        const vehicleTransmission = this.vehicleTransmissionRepository.create({...createVehicleTransmissionDto});
        const savedVehicleTransmission = await this.vehicleTransmissionRepository.save(vehicleTransmission);

        return {
            data: plainToInstance(VehicleTransmissionResponseDto, savedVehicleTransmission, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle transmission successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleTransmissionResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleTransmissionRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleTransmissionResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle transmissions retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleTransmissionResponseDto;
        message: string;
    }> {
        let vehicleTransmission: VehicleTransmission | null = null;

        if (isUUID(query)) {
            vehicleTransmission = await this.vehicleTransmissionRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleTransmission) {
            vehicleTransmission = await this.vehicleTransmissionRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleTransmission) {
            throw new NotFoundException(
                `Vehicle transmission with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleTransmissionResponseDto, vehicleTransmission, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle transmission retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleTransmissionDto: UpdateVehicleTransmissionDto,
    ): Promise<{ data: VehicleTransmissionResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleTransmission = await this.vehicleTransmissionRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleTransmission) {
            throw new NotFoundException('Vehicle transmission with ID or name not found.');
        }

        if (
            updateVehicleTransmissionDto.name &&
            updateVehicleTransmissionDto.name !== vehicleTransmission.name
        ) {
            const existing = await this.vehicleTransmissionRepository.findOne({
                where: {name: updateVehicleTransmissionDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle transmission with the provided name already exists.');
            }
        }

        Object.assign(vehicleTransmission, updateVehicleTransmissionDto);
        const updated = await this.vehicleTransmissionRepository.save(vehicleTransmission);

        const data = plainToInstance(VehicleTransmissionResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle transmission successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleTransmission = await this.vehicleTransmissionRepository.findOne({where: {id}});

        if (!vehicleTransmission) {
            throw new NotFoundException(`Vehicle transmission with id ${id} not found`);
        }

        await this.vehicleTransmissionRepository.remove(vehicleTransmission);

        return {message: 'The vehicle transmission successfully deleted.'};
    }
}
