import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_transmission')
export class VehicleTransmission extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle make'})
    name: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleTransmission)
    @ApiProperty({description: 'Vehicles with this transmission type', type: () => [Vehicle]})
    vehicles: Vehicle[];
}
