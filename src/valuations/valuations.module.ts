import { Module } from '@nestjs/common';
import { ValuationsService } from './valuations.service';
import { ValuationsController } from './valuations.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {Valuation} from "./entities/valuation.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([Valuation])
    ],
    controllers: [ValuationsController],
    providers: [ValuationsService],
    exports: [ValuationsService],
})
export class ValuationsModule {}
