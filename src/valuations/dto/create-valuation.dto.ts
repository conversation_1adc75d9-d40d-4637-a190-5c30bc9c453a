import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsNumber, IsOptional, IsPositive, MinLength, IsDateString } from 'class-validator';
import { ValuationStatus } from '../entities/valuation.entity';

export class CreateValuationDto {
  @ApiProperty({ description: 'The ID of the vehicle being valued', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0' })
  @IsNumber()
  @IsPositive()
  clientId: string;

  @ApiProperty({ description: 'The ID of the vehicle being valued', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0' })
  @IsNumber()
  @IsPositive()
  vehicleId: string;

  @ApiProperty({ description: 'The valuer estimated market value of the vehicle', example: 250000 })
  @IsNumber()
  @IsPositive()
  valuerMarketValue: number;

  @ApiProperty({ description: 'The valuer estimated forced sale value of the vehicle', example: 200000 })
  @IsNumber()
  @IsPositive()
  valuerForceValue: number;

  @ApiProperty({ description: 'The market value of the vehicle', example: 240000 })
  @IsNumber()
  @IsPositive()
  marketValue: number;

  @ApiProperty({ description: 'The forced sale value of the vehicle', example: 190000 })
  @IsNumber()
  @IsPositive()
  forceValue: number;

  @ApiProperty({ description: 'The currency of the valuation', example: 'KES', default: 'KES' })
  @IsString()
  @MinLength(3)
  @IsOptional()
  currency?: string = 'KES';

  @ApiProperty({
    description: 'The status of the valuation',
    enum: ValuationStatus,
    example: ValuationStatus.PENDING
  })
  @IsEnum(ValuationStatus)
  @IsOptional()
  status?: ValuationStatus = ValuationStatus.PENDING;

  @ApiProperty({ description: 'The ID of the user who created the valuation', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0' })
  @IsString()
  @IsNotEmpty()
  createdById: string;

  @ApiProperty({ description: 'Additional notes about the valuation', example: 'Vehicle in excellent condition' })
  @IsString()
  @IsOptional()
  remarks?: string;

  @ApiProperty({ description: 'Body condition notes', required: false })
  @IsString()
  @IsOptional()
  bodyCondition?: string;

  @ApiProperty({ description: 'Engine condition notes', required: false })
  @IsString()
  @IsOptional()
  engineCondition?: string;

  @ApiProperty({ description: 'Transmission condition notes', required: false })
  @IsString()
  @IsOptional()
  transCondition?: string;

  @ApiProperty({ description: 'Cooling system notes', required: false })
  @IsString()
  @IsOptional()
  coolingSystem?: string;

  @ApiProperty({ description: 'Steering system notes', required: false })
  @IsString()
  @IsOptional()
  steeringSystem?: string;

  @ApiProperty({ description: 'Electrical system notes', required: false })
  @IsString()
  @IsOptional()
  electricalSystem?: string;

  @ApiProperty({ description: 'Braking system notes', required: false })
  @IsString()
  @IsOptional()
  brakingSystem?: string;

  @ApiProperty({ description: 'Suspension system notes', required: false })
  @IsString()
  @IsOptional()
  suspensionSystem?: string;

  @ApiProperty({ description: 'General condition notes', required: false })
  @IsString()
  @IsOptional()
  generalCondition?: string;

  @ApiProperty({ description: 'Test drive notes', required: false })
  @IsString()
  @IsOptional()
  testDrive?: string;

  @ApiProperty({ description: 'Nota Bene (important note)', required: false })
  @IsString()
  @IsOptional()
  notaBene?: string;

  @ApiProperty({ description: 'The insurance company', required: false })
  @IsString()
  @IsOptional()
  insuranceCompany?: string;

  @ApiProperty({ description: 'The insurance policy number', required: false })
  @IsString()
  @IsOptional()
  policyNumber?: string;

  @ApiProperty({ description: 'Insurance expiry date', required: false })
  @IsDateString()
  @IsOptional()
  insuranceExpiry?: Date;

  @ApiProperty({ description: 'Issued to', required: false })
  @IsString()
  @IsOptional()
  insuranceIssuedTo?: string;
}
