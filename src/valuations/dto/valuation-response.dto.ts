import {Expose, Type} from "class-transformer";
import {ApiProperty} from "@nestjs/swagger";
import {ValuationStatus} from "../entities/valuation.entity";
import {ClientResponseDto} from "../../client/dto/client-response.dto";
import {CompanyResponseDto} from "../../company/dto/company-response.dto";
import {VehicleResponseDto} from "../../vehicles/dto/vehicle-response.dto";

export class ValuationResponseDto {
    @Expose()
    id: string;

    @Expose()
    clientId: string;

    @ApiProperty({type: () => ClientResponseDto, required: false})
    @Expose()
    @Type(() => ClientResponseDto)
    valuationClient?: ClientResponseDto;

    @Expose()
    vehicleId: string;

    @ApiProperty({type: () => VehicleResponseDto, required: false})
    @Expose()
    @Type(() => VehicleResponseDto)
    valuationVehicle?: VehicleResponseDto;

    @Expose()
    companyId?: string;

    @ApiProperty({type: () => CompanyResponseDto, required: false})
    @Expose()
    @Type(() => CompanyResponseDto)
    businessSource?: CompanyResponseDto;

    @Expose()
    insuranceCompany?: string;

    @Expose()
    policyNumber?: string;

    @Expose()
    insuranceExpiry?: Date;

    @Expose()
    insuranceIssuedTo?: string;

    @Expose()
    bodyCondition?: string;

    @Expose()
    engineCondition?: string;

    @Expose()
    transCondition?: string;

    @Expose()
    coolingSystem?: string;

    @Expose()
    steeringSystem?: string;

    @Expose()
    electricalSystem?: string;

    @Expose()
    brakingSystem?: string;

    @Expose()
    suspensionSystem?: string;

    @Expose()
    generalCondition?: string;

    @Expose()
    testDrive?: string;

    @Expose()
    remarks?: string;

    @Expose()
    notaBene?: string;

    @Expose()
    valuerMarketValue?: number;

    @Expose()
    valuerForceValue?: number;

    @Expose()
    marketValue?: number;

    @Expose()
    forceValue?: number;

    @Expose()
    currency: string;

    @Expose()
    status: ValuationStatus;

    @Expose()
    createdById: string;

    @Expose()
    scheduledById?: string;

    @Expose()
    scheduledOn?: Date;

    @Expose()
    valuedById?: string;

    @Expose()
    valuedOn?: Date;

    @Expose()
    approvedById?: string;

    @Expose()
    approvedOn?: Date;

    @Expose()
    modifiedById?: string;

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;

    @Expose()
    deletedAt?: Date;
}