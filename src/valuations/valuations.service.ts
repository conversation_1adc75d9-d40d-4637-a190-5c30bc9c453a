import { Injectable } from '@nestjs/common';
import { CreateValuationDto } from './dto/create-valuation.dto';
import { UpdateValuationDto } from './dto/update-valuation.dto';

@Injectable()
export class ValuationsService {
  create(createValuationDto: CreateValuationDto) {
    return 'This action adds a new valuation';
  }

  findAll() {
    return `This action returns all valuations`;
  }

  findOne(id: number) {
    return `This action returns a #${id} valuation`;
  }

  update(id: number, updateValuationDto: UpdateValuationDto) {
    return `This action updates a #${id} valuation`;
  }

  remove(id: number) {
    return `This action removes a #${id} valuation`;
  }
}
