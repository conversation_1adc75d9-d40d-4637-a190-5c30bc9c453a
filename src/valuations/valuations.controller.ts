import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ValuationsService } from './valuations.service';
import { CreateValuationDto } from './dto/create-valuation.dto';
import { UpdateValuationDto } from './dto/update-valuation.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Valuation } from './entities/valuation.entity';

@ApiTags('valuations')
@Controller('valuations')
export class ValuationsController {
  constructor(private readonly valuationsService: ValuationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new valuation' })
  @ApiResponse({ status: 201, description: 'The valuation has been successfully created.', type: Valuation })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createValuationDto: CreateValuationDto) {
    return this.valuationsService.create(createValuationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all valuations' })
  @ApiResponse({ status: 200, description: 'Return all valuations.', type: [Valuation] })
  findAll() {
    return this.valuationsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a valuation by id' })
  @ApiParam({ name: 'id', description: 'Valuation ID' })
  @ApiResponse({ status: 200, description: 'Return the valuation.', type: Valuation })
  @ApiResponse({ status: 404, description: 'Valuation not found.' })
  findOne(@Param('id') id: string) {
    return this.valuationsService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a valuation' })
  @ApiParam({ name: 'id', description: 'Valuation ID' })
  @ApiResponse({ status: 200, description: 'The valuation has been successfully updated.', type: Valuation })
  @ApiResponse({ status: 404, description: 'Valuation not found.' })
  update(@Param('id') id: string, @Body() updateValuationDto: UpdateValuationDto) {
    return this.valuationsService.update(+id, updateValuationDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a valuation' })
  @ApiParam({ name: 'id', description: 'Valuation ID' })
  @ApiResponse({ status: 200, description: 'The valuation has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Valuation not found.' })
  remove(@Param('id') id: string) {
    return this.valuationsService.remove(+id);
  }
}
