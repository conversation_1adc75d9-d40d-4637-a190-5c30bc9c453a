import {ApiProperty} from '@nestjs/swagger';
import {Column, Entity, Join<PERSON><PERSON>umn, ManyToOne} from 'typeorm';
import {BaseEntity} from '../../database/entities/base.entity';
import {VehicleMake} from "../../vehicle-make/entities/vehicle-make.entity";
import {Client} from "../../client/entities/client.entity";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";
import {Company} from "../../company/entities/company.entity";
import {User} from "../../users/entities/user.entity";

export enum ValuationStatus {
    PENDING = 'PENDING',
    BOOKED = 'BOOKED',
    SCHEDULED = 'SCHEDULED',
    VALUED = 'VALUED',
    APPROVED = 'APPROVED',
    COMPLETED = 'COMPLETED',
    REJECTED = 'REJECTED',
    CANCELLED = 'CANCELLED',
}

@Entity('valuation')
export class Valuation extends BaseEntity {
    @Column()
    @ApiProperty({description: 'The insurance company the vehicle is insured to'})
    insuranceCompany?: string;

    @Column()
    @ApiProperty({description: 'The insurance policy number'})
    policyNumber?: string;

    @Column()
    @ApiProperty({description: 'The insurance expiry date'})
    insuranceExpiry?: Date;

    @Column()
    @ApiProperty({description: 'The individual the insurance in issued'})
    insuranceIssuedTo?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on body condition of the vehicle valuation', required: false})
    bodyCondition?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on engine condition of the vehicle valuation', required: false})
    engineCondition?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on transmission condition of the vehicle valuation', required: false})
    transCondition?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on cooling system of the vehicle valuation', required: false})
    coolingSystem?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on steering system of the vehicle valuation', required: false})
    steeringSystem?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on electrical system of the vehicle valuation', required: false})
    electricalSystem?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on braking system of the vehicle valuation', required: false})
    brakingSystem?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on suspension system condition of the vehicle valuation', required: false})
    suspensionSystem?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on general condition of the vehicle valuation', required: false})
    generalCondition?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes on test drive of the vehicle valuation', required: false})
    testDrive?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Remarks of the vehicle valuation', required: false})
    remarks?: string;

    @Column({type: 'text', nullable: true})
    @ApiProperty({description: 'Notes to draw attention on the vehicle valuation', required: false})
    notaBene?: string;

    @Column('decimal', {precision: 12, scale: 2})
    @ApiProperty({description: 'The estimated valuer market value of the vehicle'})
    valuerMarketValue?: number;

    @Column('decimal', {precision: 12, scale: 2})
    @ApiProperty({description: 'The estimated valuer forced sale value of the vehicle'})
    valuerForceValue?: number;

    @Column('decimal', {precision: 12, scale: 2})
    @ApiProperty({description: 'The estimated market value of the vehicle'})
    marketValue?: number;

    @Column('decimal', {precision: 12, scale: 2})
    @ApiProperty({description: 'The estimated forced sale value of the vehicle'})
    forceValue?: number;

    @Column({default: 'KES'})
    @ApiProperty({description: 'The currency of the valuation', example: 'KES', default: 'KES'})
    currency: string;

    @Column({type: 'enum', enum: ValuationStatus, default: ValuationStatus.PENDING})
    @ApiProperty({description: 'The status of the valuation', enum: ValuationStatus})
    status: ValuationStatus;

    @Column()
    @ApiProperty({description: 'The date when the entity was scheduled'})
    scheduledOn?: Date;

    @Column()
    @ApiProperty({description: 'The date when the entity was valued'})
    valuedOn?: Date;

    @Column()
    @ApiProperty({description: 'The date when the entity was approved'})
    approvedOn?: Date;

    @Column()
    @ApiProperty({description: 'The ID of the client who requested valuation'})
    clientId: string;

    @ManyToOne(() => Client, (client) => client.valuations, {eager: true})
    @JoinColumn({name: 'clientId'})
    @ApiProperty({description: 'The client associated with the valuation', type: () => Client})
    client: Client;

    @Column()
    @ApiProperty({description: 'The ID of the vehicle to be valued'})
    vehicleId: string;

    @ManyToOne(() => Vehicle, (vehicle) => vehicle.valuations, {eager: true})
    @JoinColumn({name: 'vehicleId'})
    @ApiProperty({description: 'The vehicle associated with the valuation', type: () => Vehicle})
    vehicle: Vehicle;

    @Column()
    @ApiProperty({description: 'The ID of the company who requested valuation'})
    companyId?: string;

    @ManyToOne(() => Company, (company) => company.valuations, {eager: true})
    @JoinColumn({name: 'companyId'})
    @ApiProperty({description: 'The company associated with the valuation', type: () => Company})
    company: Company;

    @Column()
    @ApiProperty({description: 'The ID of the user who created the valuation'})
    createdById: string;

    @ManyToOne(() => User, (user) => user.createdValuations, {eager: true})
    @JoinColumn({name: 'createdById'})
    @ApiProperty({description: 'The user associated with the valuation', type: () => User})
    createdBy: User;

    @Column({ nullable: true })
    @ApiProperty({description: 'The ID of the user who booked and scheduled the valuation'})
    scheduledById?: string;

    @ManyToOne(() => User, (user) => user.scheduledValuations, {eager: true})
    @JoinColumn({name: 'scheduledById'})
    @ApiProperty({description: 'The user associated with the valuation', type: () => User})
    scheduledBy: User;

    @Column({ nullable: true })
    @ApiProperty({description: 'The ID of the user who did the valuation the valuation'})
    valuedById?: string;

    @ManyToOne(() => User, (user) => user.valuedValuations, {eager: true})
    @JoinColumn({name: 'valuedById'})
    @ApiProperty({description: 'The user associated with the valuation', type: () => User})
    valuedBy: User;

    @Column({ nullable: true })
    @ApiProperty({description: 'The ID of the user who approved the valuation'})
    approvedById?: string;

    @ManyToOne(() => User, (user) => user.approvedValuations, {eager: true})
    @JoinColumn({name: 'approvedById'})
    @ApiProperty({description: 'The user associated with the valuation', type: () => User})
    approvedBy: User;

    @Column({ nullable: true })
    @ApiProperty({description: 'The ID of the user who Modified the valuation'})
    modifiedById?: string;

    @ManyToOne(() => User, (user) => user.modifiedValuations, {eager: true})
    @JoinColumn({name: 'modifiedById'})
    @ApiProperty({description: 'The user associated with the valuation', type: () => User})
    modifiedBy: User;
}
