import VuesticLogo from './VuesticLogo.vue'

export default {
  title: 'SmartWorthLogo',
  component: VuesticLogo,
  tags: ['autodocs'],
}

export const Default = () => ({
  components: { VuesticLogo },
  template: `<VuesticLogo start="#009688" end="#43A047" />`,
})

export const White = () => ({
  components: { VuesticLogo },
  template: `<div class="bg-primary">
    <VuesticLogo start="#FFF" :gradient="false"/>
  </div>`,
})

export const Teal = () => ({
  components: { VuesticLogo },
  template: `<VuesticLogo start="#009688" :gradient="false"/>`,
})

export const Large = () => ({
  components: { VuesticLogo },
  template: `<VuesticLogo start="#009688" end="#43A047" :height="48"/>`,
})

export const NoGradient = () => ({
  components: { VuesticLogo },
  template: `<VuesticLogo start="#009688" :gradient="false"/>`,
})
