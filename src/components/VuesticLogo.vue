<template>
  <div class="smart-worth-logo" :style="{ height: height + 'px' }">
    <span
      class="logo-text"
      :style="{
        color: colorsComputed.start,
        fontSize: fontSize + 'px',
        background: gradient ? `linear-gradient(90deg, ${colorsComputed.end}, ${colorsComputed.start})` : 'none',
        WebkitBackgroundClip: gradient ? 'text' : 'none',
        WebkitTextFillColor: gradient ? 'transparent' : colorsComputed.start,
        backgroundClip: gradient ? 'text' : 'none'
      }"
    >
      SmartWorth
    </span>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useColors } from 'vuestic-ui'

const { getColor } = useColors()

const props = withDefaults(
  defineProps<{
    height?: number
    start?: string
    end?: string
  }>(),
  {
    height: 18,
    start: 'primary',
    end: undefined,
  },
)

const colorsComputed = computed(() => {
  return {
    start: getColor(props.start),
    end: getColor(props.end || props.start),
  }
})
</script>
