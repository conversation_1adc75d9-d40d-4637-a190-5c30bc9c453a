<template>
  <div class="color-palette p-4">
    <h2 class="text-2xl font-bold mb-4">Color Palette Test</h2>
    
    <!-- Primary Colors -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Primary Colors</h3>
      <div class="flex gap-4 flex-wrap">
        <VaButton color="primary">Primary (#009688)</VaButton>
        <VaButton color="secondary">Secondary (#3949AB)</VaButton>
        <VaButton color="success">Success (#43A047)</VaButton>
      </div>
    </div>

    <!-- Status Colors -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Status Colors</h3>
      <div class="flex gap-4 flex-wrap">
        <VaButton color="info">Info (#2196F3)</VaButton>
        <VaButton color="warning">Warning (#FF9800)</VaButton>
        <VaButton color="danger">Danger (#F44336)</VaButton>
      </div>
    </div>

    <!-- Badges -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Badge Colors</h3>
      <div class="flex gap-2 flex-wrap">
        <VaBadge color="#009688" text="Teal" />
        <VaBadge color="#3949AB" text="Indigo" />
        <VaBadge color="#43A047" text="Green" />
        <VaBadge color="#2196F3" text="Blue" />
        <VaBadge color="#FF9800" text="Orange" />
        <VaBadge color="#9C27B0" text="Purple" />
        <VaBadge color="#E91E63" text="Pink" />
        <VaBadge color="#795548" text="Brown" />
      </div>
    </div>

    <!-- Background Examples -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Background Examples</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <VaCard class="p-4">
          <h4 class="font-semibold mb-2">Primary Card</h4>
          <p class="text-textSecondary">This is a primary card with secondary text.</p>
        </VaCard>
        <VaCard color="backgroundCardSecondary" class="p-4">
          <h4 class="font-semibold mb-2">Secondary Card</h4>
          <p class="text-textSecondary">This is a secondary card background.</p>
        </VaCard>
      </div>
    </div>

    <!-- Text Examples -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Text Colors</h3>
      <div class="space-y-2">
        <p class="text-textPrimary">Primary text color (#212121)</p>
        <p class="text-textSecondary">Secondary text color (#757575)</p>
        <div class="bg-primary p-2 rounded">
          <p class="text-textInverted">Inverted text on primary background</p>
        </div>
      </div>
    </div>

    <!-- Form Elements -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2">Form Elements</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <VaInput label="Primary Input" placeholder="Enter text..." />
        <VaSelect 
          label="Primary Select" 
          :options="['Option 1', 'Option 2', 'Option 3']" 
          placeholder="Select option..."
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// This component is for testing the color palette
// You can import and use it in any page to verify colors are working
</script>

<style scoped>
.color-palette {
  background-color: var(--va-background-primary);
  color: var(--va-text-primary);
}
</style>
