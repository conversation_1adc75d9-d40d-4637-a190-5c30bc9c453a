export interface INavigationRoute {
    name: string;
    displayName: string;
    meta: { icon: string };
    children?: INavigationRoute[];
}

export default {
    root: {
        name: '/',
        displayName: 'navigationRoutes.home',
    },
    routes: [
        {
            name: 'dashboard',
            displayName: 'menu.dashboard',
            meta: {
                icon: 'vuestic-iconset-dashboard',
            },
        },
        {
            name: 'users',
            displayName: 'menu.users',
            meta: {
                icon: 'group',
            },
        },
        {
            name: 'roles',
            displayName: 'menu.roles',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'projects',
            displayName: 'menu.projects',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'tenants',
            displayName: 'menu.tenants',
            meta: {
                icon: 'groups',
            },
        },

        {
            name: 'companies',
            displayName: 'menu.companies',
            meta: {
                icon: 'credit_card',
            },
        },
        {
            name: 'clients',
            displayName: 'menu.clients',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-types',
            displayName: 'menu.vehicle-types',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-body-types',
            displayName: 'vehicle-body-types',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-makes',
            displayName: 'menu.vehicle-makes',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-models',
            displayName: 'menu.vehicle-models',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-fuel-types',
            displayName: 'menu.vehicle-fuel-types',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-transmissions',
            displayName: 'menu.vehicle-transmissions',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicle-lighting',
            displayName: 'menu.vehicle-lighting',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'vehicles',
            displayName: 'menu.vehicles',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'valuations',
            displayName: 'menu.valuations',
            meta: {
                icon: 'folder_shared',
            },
        },
        {
            name: 'payments',
            displayName: 'menu.payments',
            meta: {
                icon: 'credit_card',
            },
            children: [
                {
                    name: 'payment-methods',
                    displayName: 'menu.payment-methods',
                },
                {
                    name: 'pricing-plans',
                    displayName: 'menu.pricing-plans',
                },
                {
                    name: 'billing',
                    displayName: 'menu.billing',
                },
            ],
        },
        {
            name: 'auth',
            displayName: 'menu.auth',
            meta: {
                icon: 'login',
            },
            children: [
                {
                    name: 'login',
                    displayName: 'menu.login',
                },
                {
                    name: 'signup',
                    displayName: 'menu.signup',
                },
                {
                    name: 'recover-password',
                    displayName: 'menu.recover-password',
                },
            ],
        },

        {
            name: 'audit-logs',
            displayName: 'menu.audit-logs',
            meta: {
                icon: 'history',
            },
        },
        {
            name: 'faq',
            displayName: 'menu.faq',
            meta: {
                icon: 'quiz',
            },
        },
        {
            name: '404',
            displayName: 'menu.404',
            meta: {
                icon: 'vuestic-iconset-files',
            },
        },
        {
            name: 'preferences',
            displayName: 'menu.preferences',
            meta: {
                icon: 'manage_accounts',
            },
        },
        {
            name: 'settings',
            displayName: 'menu.settings',
            meta: {
                icon: 'settings',
            },
        },
    ] as INavigationRoute[],
};
