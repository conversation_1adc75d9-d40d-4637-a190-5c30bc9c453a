import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class CreatePermissionDto {
  @ApiProperty({ description: 'The name of the permission' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'The description of the permission', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'The module this permission belongs to' })
  @IsString()
  @IsNotEmpty()
  module: string;

  @ApiProperty({ description: 'The action this permission represents' })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({ description: 'Whether the permission is active', default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
} 