import { Role } from '../entities/role.entity';
import { Tenant } from '../../tenant/entities/tenant.entity';
import { Company } from '../../company/entities/company.entity';
import { Expose, Type } from 'class-transformer';

export class UserResponseDto {
    @Expose()
    id: string;

    @Expose()
    username: string;

    @Expose()
    email: string;

    @Expose()
    firstName: string;

    @Expose()
    lastName: string;

    @Expose()
    phone: string;

    @Expose()
    isActive: boolean;

    @Expose()
    @Type(() => Role)
    roles: Role[];

    @Expose()
    @Type(() => Tenant)
    tenants: Tenant[];

    @Expose()
    @Type(() => Company)
    companies: Company[];

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;

    @Expose()
    deletedAt?: Date;
}