import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../entities/role.entity';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';

@Injectable()
export class RolesService {
    constructor(
        @InjectRepository(Role)
        private readonly roleRepository: Repository<Role>,
    ) {
    }

    async create(createRoleDto: CreateRoleDto): Promise<Role> {
        const role = this.roleRepository.create(createRoleDto);
        return this.roleRepository.save(role);
    }

    async findAll(includeDeleted: boolean = false): Promise<Role[]> {
        return this.roleRepository.find({
            withDeleted: includeDeleted,
            relations: ['permissions'],
        });
    }

    async findOne(id: string, includeDeleted: boolean = false): Promise<Role> {
        const role = await this.roleRepository.findOne({
            where: { id },
            withDeleted: includeDeleted,
            relations: ['permissions'],
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }

        return role;
    }

    async update(id: string, updateRoleDto: UpdateRoleDto): Promise<Role> {
        const role = await this.findOne(id);
        Object.assign(role, updateRoleDto);
        return this.roleRepository.save(role);
    }

    async remove(id: string): Promise<void> {
        const role = await this.findOne(id);
        await this.roleRepository.softDelete(id);
    }

    async restore(id: string): Promise<Role> {
        const role = await this.findOne(id, true);
        await this.roleRepository.restore(id);
        return this.findOne(id);
    }
} 