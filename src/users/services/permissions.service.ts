import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { CreatePermissionDto } from '../dto/create-permission.dto';
import { UpdatePermissionDto } from '../dto/update-permission.dto';

@Injectable()
export class PermissionsService {
    constructor(
        @InjectRepository(Permission)
        private readonly permissionRepository: Repository<Permission>,
    ) {
    }

    async create(createPermissionDto: CreatePermissionDto): Promise<Permission> {
        const permission = this.permissionRepository.create(createPermissionDto);
        return this.permissionRepository.save(permission);
    }

    async findAll(includeDeleted: boolean = false): Promise<Permission[]> {
        return this.permissionRepository.find({
            withDeleted: includeDeleted,
            relations: ['roles'],
        });
    }

    async findOne(id: string, includeDeleted: boolean = false): Promise<Permission> {
        const permission = await this.permissionRepository.findOne({
            where: { id },
            withDeleted: includeDeleted,
            relations: ['roles'],
        });

        if (!permission) {
            throw new NotFoundException(`Permission with ID ${id} not found`);
        }

        return permission;
    }

    async update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<Permission> {
        const permission = await this.findOne(id);
        Object.assign(permission, updatePermissionDto);
        return this.permissionRepository.save(permission);
    }

    async remove(id: string): Promise<void> {
        const permission = await this.findOne(id);
        await this.permissionRepository.softDelete(id);
    }

    async restore(id: string): Promise<Permission> {
        const permission = await this.findOne(id, true);
        await this.permissionRepository.restore(id);
        return this.findOne(id);
    }

    async findByModuleAndAction(module: string, action: string): Promise<Permission> {
        const permission = await this.permissionRepository.findOne({
            where: { module, action },
        });

        if (!permission) {
            throw new NotFoundException(`Permission for module ${module} and action ${action} not found`);
        }

        return permission;
    }
} 