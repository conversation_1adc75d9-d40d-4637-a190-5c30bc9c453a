import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { UsersService } from '../users.service';
import { CreateRoleDto } from '../dto/create-role.dto';
import { UpdateRoleDto } from '../dto/update-role.dto';
import { Role } from '../entities/role.entity';

@ApiTags('users')
@Controller('roles')
export class RolesController {
    constructor(private readonly usersService: UsersService) {}

    @Post()
    @ApiOperation({ summary: 'Create a new role' })
    @ApiResponse({ 
        status: 201, 
        description: 'The role has been successfully created.',
        type: Role
    })
    @ApiResponse({ status: 400, description: 'Bad Request.' })
    @ApiResponse({ status: 409, description: 'Role already exists.' })
    create(@Body() createRoleDto: CreateRoleDto) {
        return this.usersService.createRole(createRoleDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get all roles' })
    @ApiResponse({ 
        status: 200, 
        description: 'Return all roles.',
        type: [Role]
    })
    findAll() {
        return this.usersService.findAllRoles();
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a role by id' })
    @ApiParam({ name: 'id', description: 'Role ID' })
    @ApiResponse({ 
        status: 200, 
        description: 'Return the role.',
        type: Role
    })
    @ApiResponse({ status: 404, description: 'Role not found.' })
    findOne(@Param('id') id: string) {
        return this.usersService.findRole(id);
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Update a role' })
    @ApiParam({ name: 'id', description: 'Role ID' })
    @ApiResponse({ 
        status: 200, 
        description: 'The role has been successfully updated.',
        type: Role
    })
    @ApiResponse({ status: 404, description: 'Role not found.' })
    @ApiResponse({ status: 409, description: 'Role name already exists.' })
    update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
        return this.usersService.updateRole(id, updateRoleDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete a role' })
    @ApiParam({ name: 'id', description: 'Role ID' })
    @ApiResponse({ 
        status: 200, 
        description: 'The role has been successfully deleted.'
    })
    @ApiResponse({ status: 404, description: 'Role not found.' })
    remove(@Param('id') id: string) {
        return this.usersService.removeRole(id);
    }
} 