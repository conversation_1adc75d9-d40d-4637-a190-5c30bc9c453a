import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UsersService } from '../users.service';
import { CreatePermissionDto } from '../dto/create-permission.dto';
import { UpdatePermissionDto } from '../dto/update-permission.dto';

@ApiTags('users')
@Controller('permissions')
export class PermissionsController {
    constructor(private readonly usersService: UsersService) {}

    @Post()
    @ApiOperation({ summary: 'Create a new permission' })
    @ApiResponse({ status: 201, description: 'Permission created successfully' })
    create(@Body() createPermissionDto: CreatePermissionDto) {
        return this.usersService.createPermission(createPermissionDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get all permissions' })
    @ApiResponse({ status: 200, description: 'Return all permissions' })
    findAll() {
        return this.usersService.findAllPermissions();
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a permission by id' })
    @ApiResponse({ status: 200, description: 'Return the permission' })
    findOne(@Param('id') id: string) {
        return this.usersService.findPermission(id);
    }

    @Patch(':id')
    @ApiOperation({ summary: 'Update a permission' })
    @ApiResponse({ status: 200, description: 'Permission updated successfully' })
    update(@Param('id') id: string, @Body() updatePermissionDto: UpdatePermissionDto) {
        return this.usersService.updatePermission(id, updatePermissionDto);
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete a permission' })
    @ApiResponse({ status: 200, description: 'Permission deleted successfully' })
    remove(@Param('id') id: string) {
        return this.usersService.removePermission(id);
    }
} 