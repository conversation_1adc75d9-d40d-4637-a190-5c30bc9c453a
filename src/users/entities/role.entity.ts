import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON>, JoinT<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from './user.entity';
import { Permission } from './permission.entity';
import { BaseEntity } from '../../database/entities/base.entity';

@Entity('roles')
export class Role extends BaseEntity {
  @Column({ unique: true })
  @ApiProperty({ description: 'The unique name of the role' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'The description of the role' })
  description: string;

  @ManyToMany(() => User, user => user.roles)
  users: User[];

  @ManyToMany(() => Permission, permission => permission.roles)
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' }
  })
  @ApiProperty({ description: 'The permissions associated with this role' })
  permissions: Permission[];
} 