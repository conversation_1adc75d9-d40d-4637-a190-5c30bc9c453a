import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>any } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from './role.entity';
import { BaseEntity } from '../../database/entities/base.entity';

@Entity('permissions')
export class Permission extends BaseEntity {
  @Column({ unique: true })
  @ApiProperty({ description: 'The unique name of the permission' })
  name: string;

  @Column({ nullable: true })
  @ApiProperty({ description: 'The description of the permission' })
  description: string;

  @Column()
  @ApiProperty({ description: 'The module this permission belongs to' })
  module: string;

  @Column()
  @ApiProperty({ description: 'The action this permission represents (e.g., create, read, update, delete)' })
  action: string;

  @ManyToMany(() => Role, role => role.permissions)
  @ApiProperty({ description: 'The roles that have this permission' })
  roles: Role[];
} 