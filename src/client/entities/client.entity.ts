import {Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>, ManyToMany, OneToMany} from 'typeorm';
import {ApiProperty} from '@nestjs/swagger';
import {BaseEntity} from '../../database/entities/base.entity';
import {Company} from "../../company/entities/company.entity";
import {Tenant} from "../../tenant/entities/tenant.entity";
import {Valuation} from "../../valuations/entities/valuation.entity";

// import { Tenant } from '../../tenant/entities/tenant.entity';

export enum ClientType {
    INDIVIDUAL = 'INDIVIDUAL',
    COMPANY = 'COMPANY',
}

@Entity('clients')
export class Client extends BaseEntity {
    @Column({type: 'enum', enum: ClientType})
    @ApiProperty({enum: ClientType, description: 'Whether the client is an individual or a company'})
    clientType: ClientType;

    @Column({nullable: true})
    @ApiProperty({description: 'Company name of the client or company'})
    companyName: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Last name of the client or company'})
    firstName: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Middle name of the client or company'})
    middleName: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Last name of the client or company'})
    lastName: string;

    @Column({unique: true})
    @ApiProperty({description: 'Client email address'})
    email: string;

    @Column()
    @ApiProperty({description: 'Primary phone number'})
    phone: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Alternative phone number', required: false})
    altPhone?: string;

    @Column()
    @ApiProperty({description: 'Street or physical address'})
    address: string;

    @Column()
    @ApiProperty({description: 'City'})
    city: string;

    @Column()
    @ApiProperty({description: 'Country'})
    country: string;

    @Column()
    @ApiProperty({description: 'Postal or ZIP code'})
    postalCode: string;

    @Column({nullable: true})
    @ApiProperty({description: 'National ID (for individuals)', required: false})
    nationalId?: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Company registration number (for companies)', required: false})
    companyRegistrationNo?: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Tax Identification Number (TIN)', required: false})
    taxIdNumber?: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Name of contact person (for companies)', required: false})
    contactPersonName?: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Designation of the contact person', required: false})
    contactPersonDesignation?: string;

    @ManyToMany(() => Company, company => company.clients, {cascade: true})
    @JoinTable({
        name: 'client_companies',
        joinColumn: {name: 'client_id', referencedColumnName: 'id'},
        inverseJoinColumn: {name: 'company_id', referencedColumnName: 'id'},
    })
    @ApiProperty({description: 'Companies associated with this client', type: () => [Company]})
    companies: Company[];

    @ManyToMany(() => Tenant, tenant => tenant.clients, {cascade: true})
    @JoinTable({
        name: 'tenant_clients',
        joinColumn: {name: 'client_id', referencedColumnName: 'id'},
        inverseJoinColumn: {name: 'tenant_id', referencedColumnName: 'id'},
    })
    @ApiProperty({description: 'Tenants this client is associated with', type: () => [Tenant]})
    tenants: Tenant[];

    @OneToMany(() => Valuation, (valuation) => valuation.client)
    @ApiProperty({description: 'Valuations associated with this client', type: () => [Valuation]})
    valuations: Valuation[];
}
