import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateClientDto, ClientType} from './dto/create-client.dto';
import {ClientResponseDto} from "./dto/client-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Client} from "./entities/client.entity";
import {Tenant} from "../tenant/entities/tenant.entity";
import {Company} from "../company/entities/company.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateClientDto} from "./dto/update-client.dto";
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class ClientService {
    private readonly logger = new Logger(ClientService.name);

    constructor(
        @InjectRepository(Client) private readonly clientRepository: Repository<Client>,
        @InjectRepository(Tenant) private readonly tenantRepository: Repository<Tenant>,
        @InjectRepository(Company) private readonly companyRepository: Repository<Company>,
    ) {
    }

    async create(createClientDto: CreateClientDto): Promise<ApiResponse<ClientResponseDto>> {
        const clientIdentifier = createClientDto.clientType === ClientType.COMPANY
            ? createClientDto.companyName
            : `${createClientDto.firstName} ${createClientDto.lastName}`;

        this.logger.log(`Creating new ${createClientDto.clientType.toLowerCase()} client: ${clientIdentifier}`);

        try {
            // Validate tenant and company relationships
            await this.validateTenantAndCompanyRelationships(createClientDto.tenantIds, createClientDto.companyIds);

            // Check for an existing client based on a client type
            await this.checkExistingClient(createClientDto);

            // Extract relationship IDs from DTO
            const { tenantIds, companyIds, ...clientData } = createClientDto;

            // Create a client entity
            const client = this.clientRepository.create(clientData);

            // Fetch related entities
            const tenants = await this.tenantRepository.findByIds(tenantIds);
            const companies = await this.companyRepository.findByIds(companyIds);

            // Assign relationships
            client.tenants = tenants;
            client.companies = companies;

            let savedClient;
            try {
                savedClient = await this.clientRepository.save(client);
            } catch (error) {
                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                // Unique violation
                if (error.code === '23505') {
                    throw new ConflictException('A client with this information already exists');
                }

                this.logger.error('Unexpected error while saving client:', error);
                throw new InternalServerErrorException('Unexpected error while creating client.');
            }

            const responseData = plainToInstance(ClientResponseDto, savedClient, {
                excludeExtraneousValues: true,
            });

            const savedClientIdentifier = savedClient.clientType === ClientType.COMPANY
                ? savedClient.companyName
                : `${savedClient.firstName} ${savedClient.lastName}`;

            this.logger.log(`Successfully created ${savedClient.clientType.toLowerCase()} client: ${savedClientIdentifier}`);

            return ResponseUtil.success(
                responseData,
                'Client created successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to create client: ${error.message}`, error.stack);
            throw error;
        }
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<PaginatedApiResponse<ClientResponseDto[]>> {
        this.logger.log(`Fetching clients - Page: ${page}, PerPage: ${perPage}, Sort: ${sort}, Order: ${order}`);
        try {
            const [records, total] = await this.clientRepository.findAndCount({
                relations: ['tenants', 'companies'],
                order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
                skip: (page - 1) * perPage,
                take: perPage,
            });

            const data = plainToInstance(ClientResponseDto, records, {
                excludeExtraneousValues: true,
            });

            const meta = {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            };

            this.logger.log(`Successfully retrieved ${records.length} clients`);

            return ResponseUtil.paginated(
                data,
                meta,
                'Clients retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to retrieve clients: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to retrieve clients');
        }
    }

    async findOneByIdOrName(query: string): Promise<ApiResponse<ClientResponseDto>> {
        this.logger.log(`Finding client by ID or name: ${query}`);
        try {
            if (!query) {
                throw new BadRequestException('Query parameter is required');
            }

            let client: Client | null = null;

            if (isUUID(query)) {
                client = await this.clientRepository.findOne({
                    where: {id: query},
                    relations: ['tenants', 'companies'],
                });
            }

            if (!client) {
                // Try to find by company name for company clients
                client = await this.clientRepository.findOne({
                    where: {
                        clientType: ClientType.COMPANY,
                        companyName: query
                    },
                    relations: ['tenants', 'companies'],
                });
            }

            if (!client) {
                // Try to find by individual name (firstName + lastName combination)
                const nameParts = query.split(' ');
                if (nameParts.length >= 2) {
                    const firstName = nameParts[0];
                    const lastName = nameParts.slice(1).join(' ');

                    client = await this.clientRepository.findOne({
                        where: {
                            clientType: ClientType.INDIVIDUAL,
                            firstName: firstName,
                            lastName: lastName
                        },
                        relations: ['tenants', 'companies'],
                    });
                }
            }

            if (!client) {
                throw new NotFoundException(
                    `Client with ID or name "${query}" not found`,
                );
            }

            const data = plainToInstance(ClientResponseDto, client, {
                excludeExtraneousValues: true,
            });

            const clientIdentifier = client.clientType === ClientType.COMPANY
                ? client.companyName
                : `${client.firstName} ${client.lastName}`;

            this.logger.log(`Successfully found ${client.clientType.toLowerCase()} client: ${clientIdentifier}`);

            return ResponseUtil.success(
                data,
                'Client retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to find client: ${error.message}`, error.stack);
            throw error;
        }
    }

    async update(
        identifier: string,
        updateClientDto: UpdateClientDto,
    ): Promise<ApiResponse<ClientResponseDto>> {
        this.logger.log(`Updating client: ${identifier}`);
        try {
            if (!identifier) {
                throw new BadRequestException('Identifier is required');
            }

            const isUuid = isUUID(identifier);
            let client: Client | null = null;

            if (isUuid) {
                client = await this.clientRepository.findOne({
                    where: {id: identifier},
                    relations: ['tenants', 'companies'],
                });
            } else {
                // Try to find by company name first
                client = await this.clientRepository.findOne({
                    where: {
                        clientType: ClientType.COMPANY,
                        companyName: identifier
                    },
                    relations: ['tenants', 'companies'],
                });

                // If not found, try to find by individual name
                if (!client) {
                    const nameParts = identifier.split(' ');
                    if (nameParts.length >= 2) {
                        const firstName = nameParts[0];
                        const lastName = nameParts.slice(1).join(' ');

                        client = await this.clientRepository.findOne({
                            where: {
                                clientType: ClientType.INDIVIDUAL,
                                firstName: firstName,
                                lastName: lastName
                            },
                            relations: ['tenants', 'companies'],
                        });
                    }
                }
            }

            if (!client) {
                throw new NotFoundException('Client with ID or name not found.');
            }

            // Check for conflicts if updating unique fields based on a client type
            if (client.clientType === ClientType.COMPANY && updateClientDto.companyName && updateClientDto.companyName !== client.companyName) {
                const existing = await this.clientRepository.findOne({
                    where: {
                        clientType: ClientType.COMPANY,
                        companyName: updateClientDto.companyName
                    },
                });

                if (existing) {
                    throw new ConflictException('A company with this name already exists.');
                }
            }

            if (client.clientType === ClientType.INDIVIDUAL) {
                // Check if updating individual details would create a duplicate
                const updatedFirstName = updateClientDto.firstName || client.firstName;
                const updatedLastName = updateClientDto.lastName || client.lastName;
                const updatedNationalId = updateClientDto.nationalId || client.nationalId;
                const updatedMiddleName = updateClientDto.middleName !== undefined ? updateClientDto.middleName : client.middleName;

                if ((updateClientDto.firstName && updateClientDto.firstName !== client.firstName) ||
                    (updateClientDto.lastName && updateClientDto.lastName !== client.lastName) ||
                    (updateClientDto.nationalId && updateClientDto.nationalId !== client.nationalId) ||
                    (updateClientDto.middleName !== undefined && updateClientDto.middleName !== client.middleName)) {

                    const whereConditions: any = {
                        clientType: ClientType.INDIVIDUAL,
                        firstName: updatedFirstName,
                        lastName: updatedLastName,
                        nationalId: updatedNationalId
                    };

                    if (updatedMiddleName) {
                        whereConditions.middleName = updatedMiddleName;
                    }

                    const existing = await this.clientRepository.findOne({
                        where: whereConditions,
                    });

                    if (existing && existing.id !== client.id) {
                        throw new ConflictException('An individual with this name and national ID already exists.');
                    }
                }
            }

            if (updateClientDto.email && updateClientDto.email !== client.email) {
                const existing = await this.clientRepository.findOne({
                    where: {email: updateClientDto.email},
                });

                if (existing) {
                    throw new ConflictException('Client with the provided email already exists.');
                }
            }

            if (updateClientDto.phone && updateClientDto.phone !== client.phone) {
                const existing = await this.clientRepository.findOne({
                    where: {phone: updateClientDto.phone},
                });

                if (existing) {
                    throw new ConflictException('Client with the provided phone number already exists.');
                }
            }

            // Handle tenant and company relationship updates
            const { tenantIds, companyIds, ...clientData } = updateClientDto;

            // Update basic client fields
            Object.assign(client, clientData);

            // Update relationships if provided
            if (tenantIds !== undefined) {
                // Validate tenant relationships
                await this.validateTenantAndCompanyRelationships(tenantIds, client.companies?.map(c => c.id) || []);

                // Fetch new tenants
                const tenants = await this.tenantRepository.findByIds(tenantIds);
                client.tenants = tenants;
            }

            if (companyIds !== undefined) {
                // Validate company relationships
                await this.validateTenantAndCompanyRelationships(client.tenants?.map(t => t.id) || [], companyIds);

                // Fetch new companies
                const companies = await this.companyRepository.findByIds(companyIds);
                client.companies = companies;
            }

            let updated;
            try {
                updated = await this.clientRepository.save(client);
            } catch (error) {
                if (error.code === '23505') {
                    throw new ConflictException('A client with this information already exists');
                }
                this.logger.error('Unexpected error while updating client:', error);
                throw new InternalServerErrorException('Unexpected error while updating client.');
            }

            const data = plainToInstance(ClientResponseDto, updated, {
                excludeExtraneousValues: true,
            });

            const updatedClientIdentifier = updated.clientType === ClientType.COMPANY
                ? updated.companyName
                : `${updated.firstName} ${updated.lastName}`;

            this.logger.log(`Successfully updated ${updated.clientType.toLowerCase()} client: ${updatedClientIdentifier}`);

            return ResponseUtil.success(
                data,
                'Client updated successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to update client: ${error.message}`, error.stack);
            throw error;
        }
    }

    async remove(id: string): Promise<ApiResponse<null>> {
        this.logger.log(`Removing client: ${id}`);
        try {
            if (!id) {
                throw new BadRequestException('ID is required');
            }

            if (!isUUID(id)) {
                throw new BadRequestException('Invalid ID format');
            }

            const client = await this.clientRepository.findOne({where: {id}});

            if (!client) {
                throw new NotFoundException(`Client with id ${id} not found`);
            }

            await this.clientRepository.remove(client);

            const clientIdentifier = client.clientType === ClientType.COMPANY
                ? client.companyName
                : `${client.firstName} ${client.lastName}`;

            this.logger.log(`Successfully removed ${client.clientType.toLowerCase()} client: ${clientIdentifier}`);

            return ResponseUtil.success(
                null,
                'Client deleted successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to remove client: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Helper methods
    private async checkExistingClient(createClientDto: CreateClientDto): Promise<void> {
        const { clientType, companyName, firstName, middleName, lastName, nationalId, email, phone } = createClientDto;

        if (clientType === ClientType.COMPANY) {
            // For companies, check if the company name already exists
            const existingCompany = await this.clientRepository.findOne({
                where: {
                    clientType: ClientType.COMPANY,
                    companyName: companyName
                },
            });

            if (existingCompany) {
                throw new ConflictException('A company with this name already exists');
            }
        } else if (clientType === ClientType.INDIVIDUAL) {
            // For individuals, check if the combination of firstName, middleName, lastName, and nationalId exists
            const whereConditions: any = {
                clientType: ClientType.INDIVIDUAL,
                firstName: firstName,
                lastName: lastName,
                nationalId: nationalId
            };

            // Only include middleName in the check if it's provided
            if (middleName) {
                whereConditions.middleName = middleName;
            }

            const existingIndividual = await this.clientRepository.findOne({
                where: whereConditions,
            });

            if (existingIndividual) {
                throw new ConflictException('An individual with this name and national ID already exists');
            }
        }

        // Always check for duplicate email and phone regardless of client type
        const existingEmail = await this.clientRepository.findOne({
            where: { email: email },
        });

        if (existingEmail) {
            throw new ConflictException('A client with this email already exists');
        }

        const existingPhone = await this.clientRepository.findOne({
            where: { phone: phone },
        });

        if (existingPhone) {
            throw new ConflictException('A client with this phone number already exists');
        }
    }

    private async validateTenantAndCompanyRelationships(tenantIds: string[], companyIds: string[]): Promise<void> {
        // Validate that all tenant IDs exist
        const tenants = await this.tenantRepository.findByIds(tenantIds);
        if (tenants.length !== tenantIds.length) {
            const foundIds = tenants.map(t => t.id);
            const missingIds = tenantIds.filter(id => !foundIds.includes(id));
            throw new BadRequestException(`The following tenant IDs were not found: ${missingIds.join(', ')}`);
        }

        // Validate that all company IDs exist
        const companies = await this.companyRepository.findByIds(companyIds);
        if (companies.length !== companyIds.length) {
            const foundIds = companies.map(c => c.id);
            const missingIds = companyIds.filter(id => !foundIds.includes(id));
            throw new BadRequestException(`The following company IDs were not found: ${missingIds.join(', ')}`);
        }
    }

    async getAvailableTenants(): Promise<ApiResponse<any[]>> {
        this.logger.log('Fetching available tenants for client association');
        try {
            const tenants = await this.tenantRepository.find({
                select: ['id', 'name', 'email'],
                order: { name: 'ASC' }
            });

            return ResponseUtil.success(
                tenants,
                'Available tenants retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to fetch available tenants: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to fetch available tenants');
        }
    }

    async getAvailableCompanies(): Promise<ApiResponse<any[]>> {
        this.logger.log('Fetching available companies for client association');
        try {
            const companies = await this.companyRepository.find({
                select: ['id', 'name', 'email'],
                order: { name: 'ASC' }
            });

            return ResponseUtil.success(
                companies,
                'Available companies retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to fetch available companies: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to fetch available companies');
        }
    }

    async checkClientExists(nationalId?: string, companyName?: string): Promise<ApiResponse<any>> {
        this.logger.log(`Checking client existence - nationalId: ${nationalId}, companyName: ${companyName}`);
        try {
            let existingClient: Client | null = null;

            if (nationalId) {
                // Check for individual client by national ID
                existingClient = await this.clientRepository.findOne({
                    where: {
                        nationalId,
                        clientType: ClientType.INDIVIDUAL
                    },
                    relations: ['tenants', 'companies']
                });
            } else if (companyName) {
                // Check for company client by company name
                existingClient = await this.clientRepository.findOne({
                    where: {
                        companyName,
                        clientType: ClientType.COMPANY
                    },
                    relations: ['tenants', 'companies']
                });
            }

            if (existingClient) {
                const responseData = plainToInstance(ClientResponseDto, existingClient, {
                    excludeExtraneousValues: true,
                });

                return ResponseUtil.success(
                    {
                        exists: true,
                        client: responseData
                    },
                    'Client found'
                );
            } else {
                return ResponseUtil.success(
                    {
                        exists: false,
                        client: null
                    },
                    'Client not found'
                );
            }
        } catch (error) {
            this.logger.error(`Failed to check client existence: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to check client existence');
        }
    }
}
