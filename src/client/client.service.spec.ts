import {Test, TestingModule} from '@nestjs/testing';
import {getRepositoryToken} from '@nestjs/typeorm';
import {Repository} from 'typeorm';
import {ConflictException} from '@nestjs/common';
import {ClientService} from './client.service';
import {Client} from './entities/client.entity';
import {ClientType, CreateClientDto} from './dto/create-client.dto';

describe('ClientService', () => {
    let service: ClientService;
    let repository: Repository<Client>;

    const mockRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOne: jest.fn(),
        findAndCount: jest.fn(),
        remove: jest.fn(),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ClientService,
                {
                    provide: getRepositoryToken(Client),
                    useValue: mockRepository,
                },
            ],
        }).compile();

        service = module.get<ClientService>(ClientService);
        repository = module.get<Repository<Client>>(getRepositoryToken(Client));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('create', () => {
        it('should create a company client successfully', async () => {
            const createCompanyDto: CreateClientDto = {
                clientType: ClientType.COMPANY,
                companyName: 'Test Company Ltd',
                email: '<EMAIL>',
                phone: '+**********',
                address: '123 Business St',
                city: 'Business City',
                country: 'Business Country',
                postalCode: '12345',
            };

            const savedClient = {
                id: '123e4567-e89b-12d3-a456-426614174000',
                ...createCompanyDto,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            mockRepository.findOne.mockResolvedValue(null); // No existing client
            mockRepository.create.mockReturnValue(savedClient);
            mockRepository.save.mockResolvedValue(savedClient);

            const result = await service.create(createCompanyDto);

            expect(result.success).toBe(true);
            expect(result.message).toBe('Client created successfully');
            expect(mockRepository.findOne).toHaveBeenCalledTimes(3); // Check company name, email, phone
        });

        it('should create an individual client successfully', async () => {
            const createIndividualDto: CreateClientDto = {
                clientType: ClientType.INDIVIDUAL,
                firstName: 'John',
                lastName: 'Doe',
                nationalId: 'ID123456789',
                email: '<EMAIL>',
                phone: '+**********',
                address: '123 Home St',
                city: 'Home City',
                country: 'Home Country',
                postalCode: '12345',
            };

            const savedClient = {
                id: '123e4567-e89b-12d3-a456-426614174001',
                ...createIndividualDto,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            mockRepository.findOne.mockResolvedValue(null); // No existing client
            mockRepository.create.mockReturnValue(savedClient);
            mockRepository.save.mockResolvedValue(savedClient);

            const result = await service.create(createIndividualDto);

            expect(result.success).toBe(true);
            expect(result.message).toBe('Client created successfully');
            expect(mockRepository.findOne).toHaveBeenCalledTimes(3); // Check individual details, email, phone
        });

        it('should throw ConflictException when company name already exists', async () => {
            const createCompanyDto: CreateClientDto = {
                clientType: ClientType.COMPANY,
                companyName: 'Existing Company',
                email: '<EMAIL>',
                phone: '+1234567891',
                address: '123 Business St',
                city: 'Business City',
                country: 'Business Country',
                postalCode: '12345',
            };

            const existingClient = {
                id: '123e4567-e89b-12d3-a456-426614174002',
                clientType: ClientType.COMPANY,
                companyName: 'Existing Company',
            };

            mockRepository.findOne.mockResolvedValueOnce(existingClient); // Company exists

            await expect(service.create(createCompanyDto)).rejects.toThrow(
                new ConflictException('A company with this name already exists')
            );
        });

        it('should throw ConflictException when individual with same details exists', async () => {
            const createIndividualDto: CreateClientDto = {
                clientType: ClientType.INDIVIDUAL,
                firstName: 'Jane',
                lastName: 'Smith',
                nationalId: 'ID987654321',
                email: '<EMAIL>',
                phone: '+1234567892',
                address: '123 Home St',
                city: 'Home City',
                country: 'Home Country',
                postalCode: '12345',
            };

            const existingClient = {
                id: '123e4567-e89b-12d3-a456-426614174003',
                clientType: ClientType.INDIVIDUAL,
                firstName: 'Jane',
                lastName: 'Smith',
                nationalId: 'ID987654321',
            };

            mockRepository.findOne
                .mockResolvedValueOnce(existingClient) // Individual exists
                .mockResolvedValueOnce(null) // Email doesn't exist
                .mockResolvedValueOnce(null); // Phone doesn't exist

            await expect(service.create(createIndividualDto)).rejects.toThrow(
                new ConflictException('An individual with this name and national ID already exists')
            );
        });

        it('should throw ConflictException when email already exists', async () => {
            const createCompanyDto: CreateClientDto = {
                clientType: ClientType.COMPANY,
                companyName: 'New Company',
                email: '<EMAIL>',
                phone: '+1234567893',
                address: '123 Business St',
                city: 'Business City',
                country: 'Business Country',
                postalCode: '12345',
            };

            const existingClient = {
                id: '123e4567-e89b-12d3-a456-426614174004',
                email: '<EMAIL>',
            };

            mockRepository.findOne
                .mockResolvedValueOnce(null) // Company doesn't exist
                .mockResolvedValueOnce(existingClient); // Email exists

            await expect(service.create(createCompanyDto)).rejects.toThrow(
                new ConflictException('A client with this email already exists')
            );
        });

        it('should throw ConflictException when phone already exists', async () => {
            const createCompanyDto: CreateClientDto = {
                clientType: ClientType.COMPANY,
                companyName: 'New Company',
                email: '<EMAIL>',
                phone: '+1234567894',
                address: '123 Business St',
                city: 'Business City',
                country: 'Business Country',
                postalCode: '12345',
            };

            const existingClient = {
                id: '123e4567-e89b-12d3-a456-426614174005',
                phone: '+1234567894',
            };

            mockRepository.findOne
                .mockResolvedValueOnce(null) // Company doesn't exist
                .mockResolvedValueOnce(null) // Email doesn't exist
                .mockResolvedValueOnce(existingClient); // Phone exists

            await expect(service.create(createCompanyDto)).rejects.toThrow(
                new ConflictException('A client with this phone number already exists')
            );
        });
    });
});
