import { Modu<PERSON> } from '@nestjs/common';
import { ClientService } from './client.service';
import { ClientController } from './client.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {Client} from "./entities/client.entity";
import {Tenant} from "../tenant/entities/tenant.entity";
import {Company} from "../company/entities/company.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([Client, Tenant, Company])
    ],
  controllers: [ClientController],
  providers: [ClientService],
  exports:[ClientService]
})
export class ClientModule {}
