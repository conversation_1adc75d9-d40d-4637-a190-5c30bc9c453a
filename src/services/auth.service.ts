import axios from 'axios';
import api from './api';
import { jwtDecode } from 'jwt-decode';


interface LoginCredentials {
    email: string;
    password: string;
}

interface AuthResponse {
    token: string;
    user: {
        id: string;
        email: string;
        name: string;
    };
}

export const authService = {
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
        try {
            const response = await axios.post(api.login(), credentials, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const token = response.data.access_token;
            const decoded: DecodedToken = jwtDecode(token);
            this.setAuthToken(token);

            return {
                token,
                user: {
                    id: decoded.sub,
                    email: decoded.email,
                    name: decoded.username,
                },
            };
        } catch (error) {
            throw error;
        }
    },

    async logout(): Promise<void> {
        try {
            await axios.post(api.logout());
            this.removeAuthToken();
        } catch (error) {
            console.error('Logout error:', error);
        }
    },

    async refreshToken(): Promise<string> {
        try {
            const response = await axios.post(api.refreshToken());
            const { access_token } = response.data;
            this.setAuthToken(access_token);
            return access_token;
        } catch (error) {
            this.removeAuthToken();
            throw error;
        }
    },

    setAuthToken(token: string) {
        localStorage.setItem('auth_token', token);
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    },

    getAuthToken(): string | null {
        return localStorage.getItem('auth_token');
    },

    removeAuthToken() {
        localStorage.removeItem('auth_token');
        delete axios.defaults.headers.common['Authorization'];
    },

    isAuthenticated(): boolean {
        return !!this.getAuthToken();
    },
};
