export const sleep = (ms = 0) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/** Validation */
export const validators = {
  email: (v: string) => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(v) || 'Please enter a valid email address'
  },
  required: (v: any) => !!v || 'This field is required',
}


// Format date
export function formatDateTime(dateStr: string): string {
  const date = new Date(dateStr)
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
}

// Convert ISO date string to YYYY-MM-DD format for HTML date inputs
export function formatDateForInput(dateStr: string): string {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  // Check if date is valid
  if (isNaN(date.getTime())) return ''
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`
}

// Convert YYYY-MM-DD format back to ISO string
export function formatDateFromInput(dateStr: string): string {
  if (!dateStr) return ''
  // Create date at noon UTC to avoid timezone issues
  const date = new Date(dateStr + 'T12:00:00.000Z')
  return date.toISOString()
}
