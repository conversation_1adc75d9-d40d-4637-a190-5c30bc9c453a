export default {
  presets: {
    light: {
      // Main colors based on your specification
      primary: '#009688',        // Teal
      secondary: '#3949AB',      // Indigo

      // Background colors
      backgroundPrimary: '#F5F5F5',     // Light gray
      backgroundSecondary: '#FFFFFF',   // White for cards/modals
      backgroundElement: '#FAFAFA',     // Slightly lighter for elements
      backgroundCardPrimary: '#FFFFFF', // White cards
      backgroundCardSecondary: '#F9F9F9', // Light gray cards
      backgroundBorder: '#E0E0E0',      // Light border

      // Text colors
      textPrimary: '#212121',    // Dark gray/black
      textSecondary: '#757575',  // Medium gray
      textInverted: '#FFFFFF',   // White text for dark backgrounds

      // Status colors (keeping functional colors meaningful)
      success: '#43A047',        // Green (your accent color)
      info: '#2196F3',          // Blue
      danger: '#F44336',        // Red
      warning: '#FF9800',       // Orange

      // Additional semantic colors
      focus: '#009688',         // Same as primary
      shadow: 'rgba(0, 0, 0, 0.12)',
    },
    dark: {
      // Dark theme with same primary colors but adjusted backgrounds
      primary: '#009688',        // Teal (same)
      secondary: '#3949AB',      // Indigo (same)

      // Dark backgrounds
      backgroundPrimary: '#121212',     // Very dark
      backgroundSecondary: '#1E1E1E',   // Dark gray
      backgroundElement: '#2C2C2C',     // Medium dark
      backgroundCardPrimary: '#1E1E1E', // Dark cards
      backgroundCardSecondary: '#2C2C2C', // Lighter dark cards
      backgroundBorder: '#404040',      // Dark border

      // Dark theme text
      textPrimary: '#FFFFFF',    // White
      textSecondary: '#B0B0B0',  // Light gray
      textInverted: '#212121',   // Dark for light backgrounds

      // Status colors (same as light)
      success: '#43A047',        // Green
      info: '#2196F3',          // Blue
      danger: '#F44336',        // Red
      warning: '#FF9800',       // Orange

      // Dark theme specific
      focus: '#009688',         // Same as primary
      shadow: 'rgba(0, 0, 0, 0.24)',
    },
  },
}
