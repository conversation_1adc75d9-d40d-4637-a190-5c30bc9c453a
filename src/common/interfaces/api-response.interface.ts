export interface ApiResponse<T = any> {
  data?: T;
  message: string;
  success: boolean;
  timestamp: string;
  path?: string;
}

export interface PaginatedApiResponse<T = any> extends ApiResponse<T> {
  meta: {
    total: number;
    page: number;
    perPage: number;
    totalPages: number;
  };
}

export interface ErrorResponse extends ApiResponse {
  error: {
    code: string;
    details?: any;
  };
}
