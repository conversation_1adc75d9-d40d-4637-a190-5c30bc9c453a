import { ApiResponse, PaginatedApiResponse, ErrorResponse } from '../interfaces/api-response.interface';

export class ResponseUtil {
  static success<T>(
    data: T,
    message: string,
    path?: string
  ): ApiResponse<T> {
    return {
      data,
      message,
      success: true,
      timestamp: new Date().toISOString(),
      path,
    };
  }

  static paginated<T>(
    data: T[],
    meta: {
      total: number;
      page: number;
      perPage: number;
      totalPages: number;
    },
    message: string,
    path?: string
  ): PaginatedApiResponse<T[]> {
    return {
      data,
      meta,
      message,
      success: true,
      timestamp: new Date().toISOString(),
      path,
    };
  }

  static error(
    message: string,
    code: string,
    details?: any,
    path?: string
  ): ErrorResponse {
    return {
      message,
      success: false,
      timestamp: new Date().toISOString(),
      path,
      error: {
        code,
        details,
      },
    };
  }
}
