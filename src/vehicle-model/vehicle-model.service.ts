import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleModelDto} from './dto/create-vehicle-model.dto';
import {VehicleModelResponseDto} from "./dto/vehicle-model-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleModel} from "./entities/vehicle-model.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleModelDto} from "./dto/update-vehicle-model.dto";
import {VehicleMake} from "../vehicle-make/entities/vehicle-make.entity";

@Injectable()
export class VehicleModelService {
    constructor(
        @InjectRepository(VehicleMake) private readonly vehicleMakeRepository: Repository<VehicleMake>,
        @InjectRepository(VehicleModel) private readonly vehicleModelRepository: Repository<VehicleModel>,
    ) {
    }

    async create(createVehicleModelDto: CreateVehicleModelDto): Promise<{
        data: VehicleModelResponseDto;
        message: string
    }> {
        const existingVehicleModel = await this.vehicleModelRepository.findOne({
            where: {name: createVehicleModelDto.name},
        });

        if (existingVehicleModel) {
            throw new ConflictException('Vehicle model with this name already exists');
        }

        const vehicleModel = this.vehicleModelRepository.create({...createVehicleModelDto});
        const savedVehicleModel = await this.vehicleModelRepository.save(vehicleModel);

        return {
            data: plainToInstance(VehicleModelResponseDto, savedVehicleModel, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle model successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
        expandDetails: boolean = false,
        vehicleMake?: string
    ): Promise<{
        data: VehicleModelResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {

        const query = this.vehicleModelRepository
            .createQueryBuilder('model')
            .orderBy(`model.${sort}`, order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC')
            .skip((page - 1) * perPage)
            .take(perPage);

        if (vehicleMake || expandDetails) {
            query.leftJoinAndSelect('model.vehicleMake', 'vehicleMake');
        }

        if (vehicleMake) {
            const isValidUuid = isUUID(vehicleMake);
            query.andWhere(
                `(LOWER(vehicleMake.name) LIKE LOWER(:make)${isValidUuid ? ' OR vehicleMake.id = :makeId' : ''})`,
                {
                    make: `%${vehicleMake}%`,
                    ...(isValidUuid ? {makeId: vehicleMake} : {}),
                },
            );
        }

        console.log('Generated SQL:', query.getSql());
        console.log('Query Parameters:', query.getParameters());

        const [records, total] = await query.getManyAndCount();

        let data = plainToInstance(VehicleModelResponseDto, records, {
            excludeExtraneousValues: true,
        });

        if (expandDetails) {
            data = data.map(({vehicleMakeId, ...rest}) => rest as VehicleModelResponseDto);
        }

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle models retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleModelResponseDto;
        message: string;
    }> {
        let vehicleModel: VehicleModel | null = null;

        if (isUUID(query)) {
            vehicleModel = await this.vehicleModelRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleModel) {
            vehicleModel = await this.vehicleModelRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleModel) {
            throw new NotFoundException(
                `Vehicle model with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleModelResponseDto, vehicleModel, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle model retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleModelDto: UpdateVehicleModelDto,
    ): Promise<{ data: VehicleModelResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleModel = await this.vehicleModelRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleModel) {
            throw new NotFoundException('Vehicle model with ID or name not found.');
        }

        if (
            updateVehicleModelDto.name &&
            updateVehicleModelDto.name !== vehicleModel.name
        ) {
            const existing = await this.vehicleModelRepository.findOne({
                where: {name: updateVehicleModelDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle model with the provided name already exists.');
            }
        }

        if (updateVehicleModelDto.makeId) {
            const make = await this.vehicleMakeRepository.findOneBy({id: updateVehicleModelDto.makeId});
            if (!make) {
                throw new NotFoundException('Provided vehicle make does not exist.');
            }
            vehicleModel.vehicleMake = make;
        }

        if (updateVehicleModelDto.name) {
            vehicleModel.name = updateVehicleModelDto.name;
        }

        const updated = await this.vehicleModelRepository.save(vehicleModel);

        let data = plainToInstance(VehicleModelResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle model successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleModel = await this.vehicleModelRepository.findOne({where: {id}});

        if (!vehicleModel) {
            throw new NotFoundException(`Vehicle model with id ${id} not found`);
        }

        await this.vehicleModelRepository.remove(vehicleModel);

        return {message: 'The vehicle model successfully deleted.'};
    }
}
