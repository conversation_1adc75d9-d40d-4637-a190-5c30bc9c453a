import {Expose, Type} from "class-transformer";
import {ApiProperty} from "@nestjs/swagger";
import {VehicleMakeResponseDto} from "../../vehicle-make/dto/vehicle-make-response.dto";

export class VehicleModelResponseDto {
    @Expose()
    id: string;

    @Expose()
    name: string;

    @Expose()
    vehicleMakeId: string;

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;

    @Expose()
    deletedAt?: Date;

    @ApiProperty({type: () => VehicleMakeResponseDto, required: false})
    @Expose()
    @Type(() => VehicleMakeResponseDto)
    vehicleMake?: VehicleMakeResponseDto;
}