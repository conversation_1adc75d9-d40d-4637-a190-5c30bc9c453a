import {Column} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {IsString} from "class-validator";

export class CreateVehicleModelDto {
    @ApiProperty({description: 'The model of the vehicle', example: 'Land Cruiser', required: true})
    @IsString()
    @Column({unique: true})
    name: string;

    @Column({name: 'makeId'})
    @ApiProperty({description: 'The make of the vehicle', example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0', required: true})
    @IsString()
    makeId: string;
}