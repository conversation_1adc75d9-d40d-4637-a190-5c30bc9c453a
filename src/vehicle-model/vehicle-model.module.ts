import { Module } from '@nestjs/common';
import { VehicleModelService } from './vehicle-model.service';
import { VehicleModelController } from './vehicle-model.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleModel} from "./entities/vehicle-model.entity";
import {VehicleMake} from "../vehicle-make/entities/vehicle-make.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleMake, VehicleModel])
    ],
  controllers: [VehicleModelController],
  providers: [VehicleModelService],
})
export class VehicleModelModule {}
