import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleModelService} from './vehicle-model.service';
import {CreateVehicleModelDto} from './dto/create-vehicle-model.dto';
import {UpdateVehicleModelDto} from './dto/update-vehicle-model.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleModel} from "./entities/vehicle-model.entity";

@ApiTags('Vehicle Model')
@Controller('vehicle-model')
export class VehicleModelController {
    constructor(private readonly vehicleModelService: VehicleModelService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle model'})
    @ApiResponse({status: 201, description: 'The vehicle model has been successfully created.', type: VehicleModel})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Vehicle model already exists.'})
    create(@Body() createVehicleModelDto: CreateVehicleModelDto) {
        return this.vehicleModelService.create(createVehicleModelDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Model'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle model retrieved successfully.',
        type: VehicleModel,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle model with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({name: 'order', required: false, enum: ['ASC', 'DESC'], example: 'ASC',})
    @ApiQuery({name: 'expandDetails', required: false, type: Boolean, description: 'Include full vehicle make object'})
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',
        @Query('expandDetails') expandDetails: boolean = false,
        @Query('vehicleMake') vehicleMake?: string,
    ) {
        return this.vehicleModelService.findAll(+page, +perPage, sort, order, expandDetails, vehicleMake);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle model by ID'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle model ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0 or Toyota',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle model retrieved successfully.',
        type: VehicleModel,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle model with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleModelService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle model'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle model ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle model successfully updated.',
        type: VehicleModel
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle model with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle model with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleModelDto: UpdateVehicleModelDto
    ) {
        return this.vehicleModelService.update(identifier, updateVehicleModelDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle model'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle model ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle model successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle model with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleModelService.remove(id);
    }

}
