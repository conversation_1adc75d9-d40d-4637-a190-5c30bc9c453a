import {BaseEntity} from "../../database/entities/base.entity";
import {Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {VehicleMake} from "../../vehicle-make/entities/vehicle-make.entity";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_model')
export class VehicleModel extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle model'})
    name: string;

    @ManyToOne(() => VehicleMake, (make) => make.models, { eager: true })
    @JoinColumn({ name: 'makeId' })
    @ApiProperty({ description: 'The make associated with the vehicle model', type: () => VehicleMake })
    vehicleMake: VehicleMake;

    @Column()
    makeId: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleModel)
    vehicles: Vehicle[];
}
