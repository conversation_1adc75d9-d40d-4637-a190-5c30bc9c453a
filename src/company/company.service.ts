import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateCompanyDto} from './dto/create-company.dto';
import {CompanyResponseDto} from "./dto/company-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Company} from "./entities/company.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateCompanyDto} from "./dto/update-company.dto";
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class CompanyService {
    private readonly logger = new Logger(CompanyService.name);

    constructor(
        @InjectRepository(Company) private readonly companyRepository: Repository<Company>,
    ) {
    }

    async create(createCompanyDto: CreateCompanyDto): Promise<ApiResponse<CompanyResponseDto>> {
        this.logger.log(`Creating new company: ${createCompanyDto.name}`);
        const {name, email, phone} = createCompanyDto;

        try {
            // Validate required fields
            this.validateRequiredFields(createCompanyDto);

            // Check for existing company
            await this.checkExistingCompany(name, email, phone);

            const company = this.companyRepository.create({...createCompanyDto});

            let savedCompany;
            try {
                savedCompany = await this.companyRepository.save(company);
            } catch (error) {
                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                // Unique violation
                if (error.code === '23505') {
                    throw new ConflictException('A company with this information already exists');
                }

                this.logger.error('Unexpected error while saving company:', error);
                throw new InternalServerErrorException('Unexpected error while creating company.');
            }

            const responseData = plainToInstance(CompanyResponseDto, savedCompany, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully created company: ${savedCompany.name}`);

            return ResponseUtil.success(
                responseData,
                'Company created successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to create company: ${error.message}`, error.stack);
            throw error;
        }
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: CompanyResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.companyRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(CompanyResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Companys retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: CompanyResponseDto;
        message: string;
    }> {
        let company: Company | null = null;

        if (isUUID(query)) {
            company = await this.companyRepository.findOne({
                where: {id: query},
            });
        }

        if (!company) {
            company = await this.companyRepository.findOne({
                where: {name: query},
            });
        }

        if (!company) {
            throw new NotFoundException(
                `Company with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(CompanyResponseDto, company, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The company retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateCompanyDto: UpdateCompanyDto,
    ): Promise<{ data: CompanyResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const company = await this.companyRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!company) {
            throw new NotFoundException('Company with ID or name not found.');
        }

        if (
            updateCompanyDto.name &&
            updateCompanyDto.name !== company.name
        ) {
            const existing = await this.companyRepository.findOne({
                where: {name: updateCompanyDto.name},
            });

            if (existing) {
                throw new ConflictException('Company with the provided name already exists.');
            }
        }

        Object.assign(company, updateCompanyDto);
        const updated = await this.companyRepository.save(company);

        const data = plainToInstance(CompanyResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The company successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const company = await this.companyRepository.findOne({where: {id}});

        if (!company) {
            throw new NotFoundException(`Company with id ${id} not found`);
        }

        await this.companyRepository.remove(company);

        return {message: 'The company successfully deleted.'};
    }

    // Helper methods
    private validateRequiredFields(createCompanyDto: CreateCompanyDto): void {
        const requiredFields: Record<string, string> = {
            name: 'Name',
            contactPerson: 'Contact person',
            email: 'Email',
            phone: 'Phone number',
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!createCompanyDto[field]) {
                throw new BadRequestException(`Missing required field: ${label}`);
            }
        }
    }

    private async checkExistingCompany(name: string, email: string, phone: string): Promise<void> {
        const existingCompany = await this.companyRepository.findOne({
            where: [{name}, {email}, {phone}],
        });

        if (existingCompany) {
            if (existingCompany.name === name) {
                throw new ConflictException('A company with this name already exists');
            }

            if (existingCompany.email === email) {
                throw new ConflictException('A company with this email already exists');
            }

            if (existingCompany.phone === phone) {
                throw new ConflictException('A company with this phone number already exists');
            }
        }
    }
}
