import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {CompanyService} from './company.service';
import {CreateCompanyDto} from './dto/create-company.dto';
import {UpdateCompanyDto} from './dto/update-company.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {Company} from "./entities/company.entity";
import { Audit } from '../audit/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../audit/entities/audit-trail.entity';

@ApiTags('Company')
@Controller('company')
export class CompanyController {
    constructor(private readonly companyService: CompanyService) {
    }

    @Post()
    @Audit({
        module: AuditModule.COMPANY,
        action: AuditAction.CREATE,
        entityName: 'Company',
        description: 'Created a new company'
    })
    @ApiOperation({summary: 'Create a new company'})
    @ApiResponse({status: 201, description: 'The company has been successfully created.', type: Company})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Company already exists.'})
    create(@Body() createCompanyDto: CreateCompanyDto) {
        return this.companyService.create(createCompanyDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Make'})
    @ApiResponse({
        status: 200,
        description: 'The company retrieved successfully.',
        type: Company,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Company with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.companyService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve company by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Company ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The company retrieved successfully.',
        type: Company,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Company with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.companyService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @Audit({
        module: AuditModule.COMPANY,
        action: AuditAction.UPDATE,
        entityName: 'Company',
        description: 'Updated company information'
    })
    @ApiOperation({summary: 'Update company'})
    @ApiParam({
        name: 'identifier',
        description: 'Company ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The company successfully updated.',
        type: Company
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Company with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Company with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateCompanyDto: UpdateCompanyDto
    ) {
        return this.companyService.update(identifier, updateCompanyDto);
    }

    @Delete(':id')
    @Audit({
        module: AuditModule.COMPANY,
        action: AuditAction.DELETE,
        entityName: 'Company',
        description: 'Deleted a company'
    })
    @ApiOperation({summary: 'Delete company'})
    @ApiParam({
        name: 'id',
        description: 'Company ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The company successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Company with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.companyService.remove(id);
    }

}
