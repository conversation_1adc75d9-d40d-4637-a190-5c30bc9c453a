import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>NotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCompanyDto {
  @ApiProperty({ description: 'The unique name of the company' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'The company contact person' })
  @IsString()
  @IsNotEmpty()
  contactPerson: string;

  @ApiProperty({ description: 'The company email address' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The company phone number' })
  @IsString()
  @IsNotEmpty()
  phone: string;
}
