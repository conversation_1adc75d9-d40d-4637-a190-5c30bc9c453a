import { IsOptional, IsString, IsEmail } from 'class-validator';
import {ApiPropertyOptional, PartialType} from '@nestjs/swagger';
import {CreateCompanyDto} from "./create-company.dto";

export class UpdateCompanyDto extends PartialType(CreateCompanyDto) {
  @ApiPropertyOptional({ description: 'The unique name of the company' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: 'The company contact person' })
  @IsString()
  @IsOptional()
  contactPerson?: string;

  @ApiPropertyOptional({ description: 'The company email address' })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ description: 'The company phone number' })
  @IsString()
  @IsOptional()
  phone?: string;
}
