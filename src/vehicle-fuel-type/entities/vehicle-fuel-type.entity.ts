import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_fuel_type')
export class VehicleFuelType extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle fuel type'})
    name: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleFuelType)
    @ApiProperty({description: 'Vehicles with this fuel type', type: () => [Vehicle]})
    vehicles: Vehicle[];
}
