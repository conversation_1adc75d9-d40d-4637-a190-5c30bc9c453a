import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleFuelTypeService} from './vehicle-fuel-type.service';
import {CreateVehicleFuelTypeDto} from './dto/create-vehicle-fuel-type.dto';
import {UpdateVehicleFuelTypeDto} from './dto/update-vehicle-fuel-type.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleFuelType} from "./entities/vehicle-fuel-type.entity";

@ApiTags('Vehicle Fuel Type')
@Controller('vehicle-fuel-type')
export class VehicleFuelTypeController {
    constructor(private readonly vehicleFuelTypeService: VehicleFuelTypeService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle fuel-type'})
    @ApiResponse({status: 201, description: 'The vehicle fuel type has been successfully created.', type: VehicleFuelType})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Vehicle fuel type already exists.'})
    create(@Body() createVehicleFuelTypeDto: CreateVehicleFuelTypeDto) {
        return this.vehicleFuelTypeService.create(createVehicleFuelTypeDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle fuel type'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle fuel type retrieved successfully.',
        type: VehicleFuelType,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle fuel type with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleFuelTypeService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle fuel type by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle fuel type ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle fuel type retrieved successfully.',
        type: VehicleFuelType,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle fuel type with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleFuelTypeService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle fuel type'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle fuel type ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle fuel type successfully updated.',
        type: VehicleFuelType
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle fuel type with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle fuel type with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleFuelTypeDto: UpdateVehicleFuelTypeDto
    ) {
        return this.vehicleFuelTypeService.update(identifier, updateVehicleFuelTypeDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle fuel type'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle fuel type ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle fuel type successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle fuel type with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleFuelTypeService.remove(id);
    }

}
