import {Module} from '@nestjs/common';
import {VehicleFuelTypeService} from './vehicle-fuel-type.service';
import {VehicleFuelTypeController} from './vehicle-fuel-type.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {VehicleFuelType} from "./entities/vehicle-fuel-type.entity";

@Module({
    imports: [
        TypeOrmModule.forFeature([VehicleFuelType])
    ],
    controllers: [VehicleFuelTypeController],
    providers: [VehicleFuelTypeService],
    exports: [VehicleFuelTypeService]
})
export class VehicleFuelTypeModule {
}
