import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleFuelTypeDto} from './dto/create-vehicle-fuel-type.dto';
import {VehicleFuelTypeResponseDto} from "./dto/vehicle-fuel-type-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleFuelType} from "./entities/vehicle-fuel-type.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleFuelTypeDto} from "./dto/update-vehicle-fuel-type.dto";

@Injectable()
export class VehicleFuelTypeService {
    constructor(
        @InjectRepository(VehicleFuelType) private readonly vehicleFuelTypeRepository: Repository<VehicleFuelType>,
    ) {
    }

    async create(createVehicleFuelTypeDto: CreateVehicleFuelTypeDto): Promise<{
        data: VehicleFuelTypeResponseDto;
        message: string
    }> {
        const existingVehicleFuelType = await this.vehicleFuelTypeRepository.findOne({
            where: {name: createVehicleFuelTypeDto.name},
        });

        if (existingVehicleFuelType) {
            throw new ConflictException('Vehicle fuel type with this name already exists');
        }

        const vehicleFuelType = this.vehicleFuelTypeRepository.create({...createVehicleFuelTypeDto});
        const savedVehicleFuelType = await this.vehicleFuelTypeRepository.save(vehicleFuelType);

        return {
            data: plainToInstance(VehicleFuelTypeResponseDto, savedVehicleFuelType, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle fuel type successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleFuelTypeResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleFuelTypeRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleFuelTypeResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle fuel types retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleFuelTypeResponseDto;
        message: string;
    }> {
        let vehicleFuelType: VehicleFuelType | null = null;

        if (isUUID(query)) {
            vehicleFuelType = await this.vehicleFuelTypeRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleFuelType) {
            vehicleFuelType = await this.vehicleFuelTypeRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleFuelType) {
            throw new NotFoundException(
                `Vehicle fuel type with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleFuelTypeResponseDto, vehicleFuelType, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle fuel type retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleFuelTypeDto: UpdateVehicleFuelTypeDto,
    ): Promise<{ data: VehicleFuelTypeResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleFuelType = await this.vehicleFuelTypeRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleFuelType) {
            throw new NotFoundException('Vehicle fuel type with ID or name not found.');
        }

        if (
            updateVehicleFuelTypeDto.name &&
            updateVehicleFuelTypeDto.name !== vehicleFuelType.name
        ) {
            const existing = await this.vehicleFuelTypeRepository.findOne({
                where: {name: updateVehicleFuelTypeDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle fuel type with the provided name already exists.');
            }
        }

        Object.assign(vehicleFuelType, updateVehicleFuelTypeDto);
        const updated = await this.vehicleFuelTypeRepository.save(vehicleFuelType);

        const data = plainToInstance(VehicleFuelTypeResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle fuel type successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleFuelType = await this.vehicleFuelTypeRepository.findOne({where: {id}});

        if (!vehicleFuelType) {
            throw new NotFoundException(`Vehicle fuel type with id ${id} not found`);
        }

        await this.vehicleFuelTypeRepository.remove(vehicleFuelType);

        return {message: 'The vehicle fuel type successfully deleted.'};
    }
}
