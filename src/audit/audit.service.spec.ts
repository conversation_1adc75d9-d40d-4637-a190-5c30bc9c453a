import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditService } from './audit.service';
import { AuditTrail, AuditAction, AuditModule } from './entities/audit-trail.entity';

describe('AuditService', () => {
  let service: AuditService;
  let repository: Repository<AuditTrail>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
      getCount: jest.fn().mockResolvedValue(0),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      getRawMany: jest.fn().mockResolvedValue([]),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditService,
        {
          provide: getRepositoryToken(AuditTrail),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<AuditService>(AuditService);
    repository = module.get<Repository<AuditTrail>>(getRepositoryToken(AuditTrail));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('log', () => {
    it('should create and save an audit log entry', async () => {
      const mockAuditEntry = {
        id: '123',
        module: AuditModule.USER,
        action: AuditAction.CREATE,
        entityName: 'User',
        description: 'Created a new user',
        userId: 'user-123',
        username: 'testuser',
        userFullName: 'Test User',
      };

      mockRepository.create.mockReturnValue(mockAuditEntry);
      mockRepository.save.mockResolvedValue(mockAuditEntry);

      const result = await service.log({
        module: AuditModule.USER,
        action: AuditAction.CREATE,
        entityName: 'User',
        description: 'Created a new user',
        userId: 'user-123',
        username: 'testuser',
        userFullName: 'Test User',
      });

      expect(mockRepository.create).toHaveBeenCalledWith({
        module: AuditModule.USER,
        action: AuditAction.CREATE,
        entityName: 'User',
        description: 'Created a new user',
        userId: 'user-123',
        username: 'testuser',
        userFullName: 'Test User',
        oldValues: undefined,
        newValues: undefined,
        ipAddress: undefined,
        userAgent: undefined,
        metadata: undefined,
        user: undefined,
      });
      expect(mockRepository.save).toHaveBeenCalledWith(mockAuditEntry);
      expect(result).toEqual(mockAuditEntry);
    });

    it('should handle errors gracefully and return null', async () => {
      mockRepository.create.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await service.log({
        module: AuditModule.USER,
        action: AuditAction.CREATE,
        entityName: 'User',
      });

      expect(result).toBeNull();
    });
  });

  describe('findAll', () => {
    it('should return paginated audit logs', async () => {
      const mockData = [
        {
          id: '1',
          module: AuditModule.USER,
          action: AuditAction.CREATE,
          createdAt: new Date(),
        },
      ];

      const queryBuilder = mockRepository.createQueryBuilder();
      queryBuilder.getManyAndCount.mockResolvedValue([mockData, 1]);

      const result = await service.findAll({
        page: 1,
        limit: 20,
      });

      expect(result).toEqual({
        data: mockData,
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      });
    });
  });

  describe('getRecentLogs', () => {
    it('should return recent audit logs', async () => {
      const mockData = [
        {
          id: '1',
          module: AuditModule.USER,
          action: AuditAction.CREATE,
          createdAt: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(mockData);

      const result = await service.getRecentLogs(10);

      expect(mockRepository.find).toHaveBeenCalledWith({
        relations: ['user'],
        order: { createdAt: 'DESC' },
        take: 10,
      });
      expect(result).toEqual(mockData);
    });
  });

  describe('getStatistics', () => {
    it('should return audit statistics', async () => {
      const result = await service.getStatistics(30);

      expect(result).toHaveProperty('totalLogs');
      expect(result).toHaveProperty('moduleStats');
      expect(result).toHaveProperty('actionStats');
      expect(result).toHaveProperty('topUsers');
      expect(result).toHaveProperty('period');
    });
  });
});
