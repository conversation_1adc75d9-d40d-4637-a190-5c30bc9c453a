import { IsEnum, IsOptional, IsString, IsObject, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

export class CreateAuditTrailDto {
    @IsEnum(AuditModule)
    @ApiProperty({ enum: AuditModule, description: 'The module where the action occurred' })
    module: AuditModule;

    @IsEnum(AuditAction)
    @ApiProperty({ enum: AuditAction, description: 'The action that was performed' })
    action: AuditAction;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'The ID of the entity that was affected' })
    entityId?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'The name/type of the entity that was affected' })
    entityName?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Description of the action performed' })
    description?: string;

    @IsOptional()
    @IsObject()
    @ApiPropertyOptional({ description: 'Previous values before the change (for updates)' })
    oldValues?: Record<string, any>;

    @IsOptional()
    @IsObject()
    @ApiPropertyOptional({ description: 'New values after the change (for creates/updates)' })
    newValues?: Record<string, any>;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'IP address of the user who performed the action' })
    ipAddress?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'User agent of the client that performed the action' })
    userAgent?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Additional metadata about the action' })
    metadata?: string;

    @IsOptional()
    @IsUUID()
    @ApiPropertyOptional({ description: 'User ID who performed the action' })
    userId?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Username of the user who performed the action' })
    username?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Full name of the user who performed the action' })
    userFullName?: string;
}
