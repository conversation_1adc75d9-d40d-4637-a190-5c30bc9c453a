import { Expose, Type } from 'class-transformer';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

export class UserResponseDto {
    @Expose()
    id: string;

    @Expose()
    username: string;

    @Expose()
    email: string;

    @Expose()
    firstName: string;

    @Expose()
    lastName: string;
}

export class AuditTrailResponseDto {
    @Expose()
    id: string;

    @Expose()
    module: AuditModule;

    @Expose()
    action: AuditAction;

    @Expose()
    entityId?: string;

    @Expose()
    entityName?: string;

    @Expose()
    description?: string;

    @Expose()
    oldValues?: Record<string, any>;

    @Expose()
    newValues?: Record<string, any>;

    @Expose()
    ipAddress?: string;

    @Expose()
    userAgent?: string;

    @Expose()
    metadata?: string;

    @Expose()
    userId?: string;

    @Expose()
    username?: string;

    @Expose()
    userFullName?: string;

    @Expose()
    @Type(() => UserResponseDto)
    user?: UserResponseDto;

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;
}
