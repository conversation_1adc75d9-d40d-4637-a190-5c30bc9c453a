import { <PERSON>Optional, <PERSON>Enum, IsString, IsDateString, IsInt, Min, Max } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

export class QueryAuditTrailDto {
    @IsOptional()
    @IsEnum(AuditModule)
    @ApiPropertyOptional({ enum: AuditModule, description: 'Filter by module' })
    module?: AuditModule;

    @IsOptional()
    @IsEnum(AuditAction)
    @ApiPropertyOptional({ enum: AuditAction, description: 'Filter by action' })
    action?: AuditAction;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Filter by user ID' })
    userId?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Filter by username' })
    username?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Filter by entity ID' })
    entityId?: string;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Filter by entity name' })
    entityName?: string;

    @IsOptional()
    @IsDateString()
    @ApiPropertyOptional({ description: 'Filter by start date (ISO string)' })
    startDate?: string;

    @IsOptional()
    @IsDateString()
    @ApiPropertyOptional({ description: 'Filter by end date (ISO string)' })
    endDate?: string;

    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @ApiPropertyOptional({ description: 'Page number', default: 1, minimum: 1 })
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    @ApiPropertyOptional({ description: 'Number of items per page', default: 20, minimum: 1, maximum: 100 })
    limit?: number = 20;

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Sort field', default: 'createdAt' })
    sortBy?: string = 'createdAt';

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.toLowerCase())
    @ApiPropertyOptional({ description: 'Sort order', enum: ['asc', 'desc'], default: 'desc' })
    sortOrder?: 'asc' | 'desc' = 'desc';

    @IsOptional()
    @IsString()
    @ApiPropertyOptional({ description: 'Search term for description or entity name' })
    search?: string;
}
