import {
    Injectable,
    Logger,
    BadRequestException,
    NotFoundException,
    InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { plainToInstance } from 'class-transformer';
import { AuditTrail, AuditAction, AuditModule } from './entities/audit-trail.entity';
import { CreateAuditTrailDto } from './dto/create-audit-trail.dto';
import { QueryAuditTrailDto } from './dto/query-audit-trail.dto';
import { AuditTrailResponseDto } from './dto/audit-trail-response.dto';
import { User } from '../users/entities/user.entity';
import { ResponseUtil } from '../common/utils/response.util';

export interface AuditLogOptions {
    module: AuditModule;
    action: AuditAction;
    entityId?: string;
    entityName?: string;
    description?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    user?: User;
    userId?: string;
    username?: string;
    userFullName?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: string;
}

export interface PaginatedAuditResult {
    data: AuditTrailResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

@Injectable()
export class AuditService {
    private readonly logger = new Logger(AuditService.name);

    constructor(
        @InjectRepository(AuditTrail)
        private readonly auditRepository: Repository<AuditTrail>,
    ) {}

    /**
     * Create an audit log entry
     */
    async log(options: AuditLogOptions): Promise<AuditTrail | null> {
        try {
            const auditEntry = this.auditRepository.create({
                module: options.module,
                action: options.action,
                entityId: options.entityId,
                entityName: options.entityName,
                description: options.description,
                oldValues: options.oldValues,
                newValues: options.newValues,
                userId: options.userId || options.user?.id,
                username: options.username || options.user?.username,
                userFullName: options.userFullName ||
                    (options.user ? `${options.user.firstName} ${options.user.lastName}`.trim() : undefined),
                ipAddress: options.ipAddress,
                userAgent: options.userAgent,
                metadata: options.metadata,
                user: options.user,
            });

            const savedEntry = await this.auditRepository.save(auditEntry);
            this.logger.log(`Audit log created: ${options.module}.${options.action} by ${options.username || 'system'}`);
            return savedEntry;
        } catch (error) {
            this.logger.error(`Failed to create audit log: ${error.message}`, error.stack);
            // Don't throw error to avoid breaking the main operation
            return null;
        }
    }

    /**
     * Create audit log from DTO
     */
    async create(createAuditTrailDto: CreateAuditTrailDto): Promise<any> {
        try {
            const auditEntry = this.auditRepository.create(createAuditTrailDto);

            let savedEntry;
            try {
                savedEntry = await this.auditRepository.save(auditEntry);
            } catch (error) {
                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                this.logger.error('Unexpected error while saving audit entry:', error);
                throw new InternalServerErrorException('Unexpected error while creating audit entry.');
            }

            const responseData = plainToInstance(AuditTrailResponseDto, savedEntry, {
                excludeExtraneousValues: true,
            });

            return ResponseUtil.success(
                responseData,
                'Audit entry created successfully',
            );
        } catch (error) {
            this.logger.error('Error in create audit entry:', error);
            throw error;
        }
    }

    /**
     * Find audit logs with pagination and filtering
     */
    async findAll(query: QueryAuditTrailDto): Promise<any> {
        try {
        const {
            module,
            action,
            userId,
            username,
            entityId,
            entityName,
            startDate,
            endDate,
            page = 1,
            limit = 20,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            search
        } = query;

        const queryBuilder: SelectQueryBuilder<AuditTrail> = this.auditRepository
            .createQueryBuilder('audit')
            .leftJoinAndSelect('audit.user', 'user');

        // Apply filters
        if (module) {
            queryBuilder.andWhere('audit.module = :module', { module });
        }

        if (action) {
            queryBuilder.andWhere('audit.action = :action', { action });
        }

        if (userId) {
            queryBuilder.andWhere('audit.userId = :userId', { userId });
        }

        if (username) {
            queryBuilder.andWhere('audit.username ILIKE :username', { username: `%${username}%` });
        }

        if (entityId) {
            queryBuilder.andWhere('audit.entityId = :entityId', { entityId });
        }

        if (entityName) {
            queryBuilder.andWhere('audit.entityName ILIKE :entityName', { entityName: `%${entityName}%` });
        }

        if (startDate) {
            queryBuilder.andWhere('audit.createdAt >= :startDate', { startDate });
        }

        if (endDate) {
            queryBuilder.andWhere('audit.createdAt <= :endDate', { endDate });
        }

        if (search) {
            queryBuilder.andWhere(
                '(audit.description ILIKE :search OR audit.entityName ILIKE :search OR audit.username ILIKE :search)',
                { search: `%${search}%` }
            );
        }

        // Apply sorting
        const validSortFields = ['createdAt', 'module', 'action', 'username', 'entityName'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        queryBuilder.orderBy(`audit.${sortField}`, sortOrder.toUpperCase() as 'ASC' | 'DESC');

        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

            // Execute query
            const [data, total] = await queryBuilder.getManyAndCount();

            // Transform to response DTOs
            const responseData = data.map(item =>
                plainToInstance(AuditTrailResponseDto, item, {
                    excludeExtraneousValues: true,
                })
            );

            const meta = {
                total,
                page,
                perPage: limit,
                totalPages: Math.ceil(total / limit),
            };

            return ResponseUtil.paginated(
                responseData,
                meta,
                'Audit logs retrieved successfully',
            );
        } catch (error) {
            this.logger.error('Error in findAll audit logs:', error);
            throw new InternalServerErrorException('Failed to retrieve audit logs');
        }
    }

    /**
     * Find a single audit log by ID
     */
    async findOne(id: string): Promise<any> {
        try {
            const auditLog = await this.auditRepository.findOne({
                where: { id },
                relations: ['user'],
            });

            if (!auditLog) {
                throw new NotFoundException(`Audit log with ID ${id} not found`);
            }

            const responseData = plainToInstance(AuditTrailResponseDto, auditLog, {
                excludeExtraneousValues: true,
            });

            return ResponseUtil.success(
                responseData,
                'Audit log retrieved successfully',
            );
        } catch (error) {
            this.logger.error('Error in findOne audit log:', error);
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException('Failed to retrieve audit log');
        }
    }

    /**
     * Get recent audit logs for timeline display
     */
    async getRecentLogs(limit: number = 10): Promise<any> {
        try {
            const auditLogs = await this.auditRepository.find({
                relations: ['user'],
                order: { createdAt: 'DESC' },
                take: limit,
            });

            const responseData = auditLogs.map(item =>
                plainToInstance(AuditTrailResponseDto, item, {
                    excludeExtraneousValues: true,
                })
            );

            return ResponseUtil.success(
                responseData,
                'Recent audit logs retrieved successfully',
            );
        } catch (error) {
            this.logger.error('Error in getRecentLogs:', error);
            throw new InternalServerErrorException('Failed to retrieve recent audit logs');
        }
    }

    /**
     * Get audit statistics
     */
    async getStatistics(days: number = 30): Promise<any> {
        try {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const queryBuilder = this.auditRepository.createQueryBuilder('audit')
            .where('audit.createdAt >= :startDate', { startDate });

        const [
            totalLogs,
            moduleStats,
            actionStats,
            userStats
        ] = await Promise.all([
            queryBuilder.getCount(),
            queryBuilder
                .select('audit.module', 'module')
                .addSelect('COUNT(*)', 'count')
                .groupBy('audit.module')
                .getRawMany(),
            queryBuilder
                .select('audit.action', 'action')
                .addSelect('COUNT(*)', 'count')
                .groupBy('audit.action')
                .getRawMany(),
            queryBuilder
                .select('audit.username', 'username')
                .addSelect('COUNT(*)', 'count')
                .where('audit.username IS NOT NULL')
                .groupBy('audit.username')
                .orderBy('COUNT(*)', 'DESC')
                .limit(10)
                .getRawMany()
        ]);

            const statistics = {
                totalLogs,
                moduleStats,
                actionStats,
                topUsers: userStats,
                period: `${days} days`
            };

            return ResponseUtil.success(
                statistics,
                'Audit statistics retrieved successfully',
            );
        } catch (error) {
            this.logger.error('Error in getStatistics:', error);
            throw new InternalServerErrorException('Failed to retrieve audit statistics');
        }
    }
}
