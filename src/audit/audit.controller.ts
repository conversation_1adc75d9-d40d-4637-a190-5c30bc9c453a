import { Controller, Get, Post, Body, Param, Query, UseFilters, Logger, ParseUUIDPipe, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { AuditService } from './audit.service';
import { CreateAuditTrailDto } from './dto/create-audit-trail.dto';
import { QueryAuditTrailDto } from './dto/query-audit-trail.dto';
import { AuditTrail } from './entities/audit-trail.entity';
import { AuditTrailResponseDto } from './dto/audit-trail-response.dto';
import { HttpExceptionFilter } from '../common/filters/http-exception.filter';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Audit Trail')
@Controller('audit')
@UseFilters(HttpExceptionFilter)
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('access-token')
export class AuditController {
    private readonly logger = new Logger(AuditController.name);

    constructor(private readonly auditService: AuditService) {}

    @Post()
    @ApiOperation({ summary: 'Create an audit log entry' })
    @ApiResponse({
        status: 201,
        description: 'The audit log has been successfully created.',
        type: AuditTrailResponseDto
    })
    @ApiResponse({ status: 400, description: 'Bad Request.' })
    @ApiResponse({ status: 500, description: 'Internal Server Error.' })
    create(@Body() createAuditTrailDto: CreateAuditTrailDto) {
        return this.auditService.create(createAuditTrailDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get audit logs with filtering and pagination' })
    @ApiResponse({
        status: 200,
        description: 'Return paginated audit logs.',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/AuditTrailResponseDto' }
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        perPage: { type: 'number' },
                        totalPages: { type: 'number' }
                    }
                },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({ status: 500, description: 'Internal Server Error.' })
    findAll(@Query() query: QueryAuditTrailDto) {
        return this.auditService.findAll(query);
    }

    @Get('recent')
    @ApiOperation({ summary: 'Get recent audit logs for timeline display' })
    @ApiQuery({ name: 'limit', required: false, description: 'Number of recent logs to return', type: Number })
    @ApiResponse({
        status: 200,
        description: 'Return recent audit logs.',
        type: [AuditTrailResponseDto]
    })
    @ApiResponse({ status: 500, description: 'Internal Server Error.' })
    getRecentLogs(@Query('limit') limit?: number) {
        return this.auditService.getRecentLogs(limit ? parseInt(limit.toString()) : 10);
    }

    @Get('statistics')
    @ApiOperation({ summary: 'Get audit statistics' })
    @ApiQuery({ name: 'days', required: false, description: 'Number of days to include in statistics', type: Number })
    @ApiResponse({
        status: 200,
        description: 'Return audit statistics.',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'object',
                    properties: {
                        totalLogs: { type: 'number' },
                        moduleStats: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    module: { type: 'string' },
                                    count: { type: 'number' }
                                }
                            }
                        },
                        actionStats: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    action: { type: 'string' },
                                    count: { type: 'number' }
                                }
                            }
                        },
                        topUsers: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    username: { type: 'string' },
                                    count: { type: 'number' }
                                }
                            }
                        },
                        period: { type: 'string' }
                    }
                },
                message: { type: 'string' },
                success: { type: 'boolean' },
                timestamp: { type: 'string' }
            }
        }
    })
    @ApiResponse({ status: 500, description: 'Internal Server Error.' })
    getStatistics(@Query('days') days?: number) {
        return this.auditService.getStatistics(days ? parseInt(days.toString()) : 30);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a specific audit log by ID' })
    @ApiParam({ name: 'id', description: 'Audit log ID (UUID)', type: String })
    @ApiResponse({
        status: 200,
        description: 'Return the audit log.',
        type: AuditTrailResponseDto
    })
    @ApiResponse({ status: 404, description: 'Audit log not found.' })
    @ApiResponse({ status: 500, description: 'Internal Server Error.' })
    findOne(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.auditService.findOne(id);
    }
}
