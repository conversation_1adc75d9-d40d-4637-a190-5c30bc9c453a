import { Entity, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../database/entities/base.entity';
import { User } from '../../users/entities/user.entity';

export enum AuditAction {
    CREATE = 'CREATE',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',
    LOGIN = 'LOGIN',
    LOGOUT = 'LOGOUT',
    VIEW = 'VIEW',
    EXPORT = 'EXPORT',
    IMPORT = 'IMPORT',
    ACTIVATE = 'ACTIVATE',
    DEACTIVATE = 'DEACTIVATE',
    APPROVE = 'APPROVE',
    REJECT = 'REJECT',
    SCHEDULE = 'SCHEDULE',
    COMPLETE = 'COMPLETE',
    CANCEL = 'CANCEL'
}

export enum AuditModule {
    USER = 'USER',
    CLIENT = 'CLIENT',
    COMPANY = 'COMPANY',
    TENANT = 'TENANT',
    VALUATION = 'VALUATION',
    VEHICLE = 'VEHICLE',
    VEHICLE_MAKE = 'VEHICLE_MAKE',
    VEHICLE_MODEL = 'VEHICLE_MODEL',
    VEHICLE_TYPE = 'VEHICLE_TYPE',
    VEHICLE_BODY_TYPE = 'VEHICLE_BODY_TYPE',
    VEHICLE_FUEL_TYPE = 'VEHICLE_FUEL_TYPE',
    VEHICLE_TRANSMISSION = 'VEHICLE_TRANSMISSION',
    VEHICLE_LIGHTING = 'VEHICLE_LIGHTING',
    ROLE = 'ROLE',
    PERMISSION = 'PERMISSION',
    AUTH = 'AUTH',
    SYSTEM = 'SYSTEM'
}

@Entity('audit_trail')
export class AuditTrail extends BaseEntity {
    @Column({ type: 'enum', enum: AuditModule })
    @ApiProperty({ enum: AuditModule, description: 'The module where the action occurred' })
    module: AuditModule;

    @Column({ type: 'enum', enum: AuditAction })
    @ApiProperty({ enum: AuditAction, description: 'The action that was performed' })
    action: AuditAction;

    @Column({ nullable: true })
    @ApiProperty({ description: 'The ID of the entity that was affected', required: false })
    entityId?: string;

    @Column({ nullable: true })
    @ApiProperty({ description: 'The name/type of the entity that was affected', required: false })
    entityName?: string;

    @Column({ type: 'text', nullable: true })
    @ApiProperty({ description: 'Description of the action performed', required: false })
    description?: string;

    @Column({ type: 'jsonb', nullable: true })
    @ApiProperty({ description: 'Previous values before the change (for updates)', required: false })
    oldValues?: Record<string, any>;

    @Column({ type: 'jsonb', nullable: true })
    @ApiProperty({ description: 'New values after the change (for creates/updates)', required: false })
    newValues?: Record<string, any>;

    @Column({ nullable: true })
    @ApiProperty({ description: 'IP address of the user who performed the action', required: false })
    ipAddress?: string;

    @Column({ nullable: true })
    @ApiProperty({ description: 'User agent of the client that performed the action', required: false })
    userAgent?: string;

    @Column({ nullable: true })
    @ApiProperty({ description: 'Additional metadata about the action', required: false })
    metadata?: string;

    @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'user_id' })
    @ApiProperty({ description: 'The user who performed the action', type: () => User, required: false })
    user?: User;

    @Column({ nullable: true })
    @ApiProperty({ description: 'User ID who performed the action', required: false })
    userId?: string;

    @Column({ nullable: true })
    @ApiProperty({ description: 'Username of the user who performed the action', required: false })
    username?: string;

    @Column({ nullable: true })
    @ApiProperty({ description: 'Full name of the user who performed the action', required: false })
    userFullName?: string;
}
