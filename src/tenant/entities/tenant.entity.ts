import {<PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from "typeorm";
import {BaseEntity} from "../../database/entities/base.entity";
import {ApiProperty} from "@nestjs/swagger";
import {Company} from "../../company/entities/company.entity";
import {Client} from "../../client/entities/client.entity";
import {User} from "../../users/entities/user.entity";

@Entity('tenant')
export class Tenant extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'Name of the tenant organization or business'})
    name: string;

    @Column({unique: true})
    @ApiProperty({description: 'Internal short code/slug used to identify the tenant'})
    code: string;

    @Column({unique: true})
    @ApiProperty({description: 'Tenant contact email address'})
    email: string;

    @Column({unique: true})
    @ApiProperty({description: 'Primary contact phone number for the tenant'})
    phone: string;

    @Column()
    @ApiProperty({description: 'Physical or mailing address of the tenant'})
    address: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Tenant website or landing page', required: false})
    website?: string;

    @Column({nullable: true})
    @ApiProperty({description: 'Logo or branding image of the tenant', required: false})
    logoUrl?: string;

    @Column({default: true})
    @ApiProperty({description: 'Status flag to indicate if the tenant is active'})
    isActive: boolean;

    @Column({nullable: true})
    @ApiProperty({description: 'Subscription or billing plan name', required: false})
    subscriptionPlan?: string;

    @Column({nullable: true, type: 'timestamp'})
    @ApiProperty({description: 'Date when the current subscription started', required: false})
    subscriptionStartDate?: Date;

    @Column({nullable: true, type: 'timestamp'})
    @ApiProperty({description: 'Date when the current subscription ends', required: false})
    subscriptionEndDate?: Date;

    @ManyToMany(() => Company, company => company.tenants, {cascade: true})
    @JoinTable({
        name: 'tenant_companies',
        joinColumn: {name: 'tenant_id', referencedColumnName: 'id'},
        inverseJoinColumn: {name: 'company_id', referencedColumnName: 'id'},
    })
    @ApiProperty({description: 'Companies associated with this tenant', type: () => [Company]})
    companies: Company[];

    @ManyToMany(() => Client, client => client.tenants)
    @ApiProperty({description: 'Clients associated with this tenant', type: () => [Client]})
    clients: Client[];

    @ManyToMany(() => User, user => user.tenants)
    @ApiProperty({description: 'Users associated with this tenant', type: () => [User]})
    users: User[];
}
