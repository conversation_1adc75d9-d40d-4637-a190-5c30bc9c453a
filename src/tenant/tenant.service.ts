import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateTenantDto} from './dto/create-tenant.dto';
import {TenantResponseDto} from "./dto/tenant-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Tenant} from "./entities/tenant.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateTenantDto} from "./dto/update-tenant.dto";
import {format} from 'date-fns';
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class TenantService {
    private readonly logger = new Logger(TenantService.name);

    constructor(
        @InjectRepository(Tenant) private readonly tenantRepository: Repository<Tenant>,
    ) {
    }

    async create(createTenantDto: CreateTenantDto): Promise<ApiResponse<TenantResponseDto>> {
        this.logger.log(`Creating new tenant: ${createTenantDto.name}`);
        const {name} = createTenantDto;

        try {
            // Validate required fields
            this.validateRequiredFields(createTenantDto);

            // Check for existing tenant
            await this.checkExistingTenant(name);

        // Generate initials once
        const initials = name
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase())
            .join('');

        let savedTenant;
        const maxRetries = 3;
        let attempt = 0;

        while (attempt < maxRetries) {
            const timestamp = format(new Date(), 'yyyyMMddHHmmssSSS'); // adds milliseconds
            const code = `${initials}${timestamp}`;

            const tenant = this.tenantRepository.create({...createTenantDto, code});

            try {
                savedTenant = await this.tenantRepository.save(tenant);
                break; // success
            } catch (error) {
                // Unique violation (e.g., code)
                if (error.code === '23505') {
                    attempt++;
                    continue;
                }

                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                this.logger.error('Unexpected error while saving tenant:', error);
                throw new InternalServerErrorException('Unexpected error while creating tenant.');
            }
        }

        if (!savedTenant) {
            throw new ConflictException('Could not generate a unique tenant code. Please try again.');
        }

        const responseData = plainToInstance(TenantResponseDto, savedTenant, {
            excludeExtraneousValues: true,
        });

        this.logger.log(`Successfully created tenant: ${savedTenant.name} with code: ${savedTenant.code}`);

        return ResponseUtil.success(
            responseData,
            'Tenant created successfully'
        );
        } catch (error) {
            this.logger.error(`Failed to create tenant: ${error.message}`, error.stack);
            throw error;
        }
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<PaginatedApiResponse<TenantResponseDto[]>> {
        this.logger.log(`Fetching tenants - Page: ${page}, PerPage: ${perPage}, Sort: ${sort}, Order: ${order}`);
        try {
            const [records, total] = await this.tenantRepository.findAndCount({
                order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
                skip: (page - 1) * perPage,
                take: perPage,
            });

            const data = plainToInstance(TenantResponseDto, records, {
                excludeExtraneousValues: true,
            });

            const meta = {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            };

            this.logger.log(`Successfully retrieved ${records.length} tenants out of ${total} total`);

            return ResponseUtil.paginated(
                data,
                meta,
                'Tenants retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to retrieve tenants: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to retrieve tenants');
        }
    }

    async findOneByIdOrName(query: string): Promise<ApiResponse<TenantResponseDto>> {
        this.logger.log(`Finding tenant by ID or name: ${query}`);
        try {
            let tenant: Tenant | null = null;

            if (isUUID(query)) {
                tenant = await this.tenantRepository.findOne({
                    where: {id: query},
                });
            }

            if (!tenant) {
                tenant = await this.tenantRepository.findOne({
                    where: {name: query},
                });
            }

            if (!tenant) {
                throw new NotFoundException(
                    `Tenant with ID or name "${query}" not found`,
                );
            }

            const data = plainToInstance(TenantResponseDto, tenant, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully found tenant: ${tenant.name}`);

            return ResponseUtil.success(
                data,
                'Tenant retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to find tenant: ${error.message}`, error.stack);
            throw error;
        }
    }

    async update(
        identifier: string,
        updateTenantDto: UpdateTenantDto,
    ): Promise<ApiResponse<TenantResponseDto>> {
        this.logger.log(`Updating tenant: ${identifier}`);
        try {
            const isUuid = isUUID(identifier);

            const tenant = await this.tenantRepository.findOne({
                where: isUuid ? {id: identifier} : {name: identifier},
            });

            if (!tenant) {
                throw new NotFoundException('Tenant with ID or name not found.');
            }

            // Check for name conflicts if name is being updated
            if (updateTenantDto.name && updateTenantDto.name !== tenant.name) {
                const existing = await this.tenantRepository.findOne({
                    where: {name: updateTenantDto.name},
                });

                if (existing) {
                    throw new ConflictException('Tenant with the provided name already exists.');
                }
            }

            Object.assign(tenant, updateTenantDto);
            const updated = await this.tenantRepository.save(tenant);

            const data = plainToInstance(TenantResponseDto, updated, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully updated tenant: ${updated.name}`);

            return ResponseUtil.success(
                data,
                'Tenant updated successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to update tenant: ${error.message}`, error.stack);
            throw error;
        }
    }

    async remove(id: string): Promise<ApiResponse<null>> {
        this.logger.log(`Deleting tenant: ${id}`);

        try {
            const tenant = await this.tenantRepository.findOne({where: {id}});

            if (!tenant) {
                throw new NotFoundException(`Tenant with id ${id} not found`);
            }

            await this.tenantRepository.softDelete(id);

            this.logger.log(`Successfully deleted tenant: ${tenant.name}`);

            return ResponseUtil.success(
                null,
                'Tenant deleted successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to delete tenant: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Helper methods
    private validateRequiredFields(createTenantDto: CreateTenantDto): void {
        const requiredFields: Record<string, string> = {
            name: 'Name',
            email: 'Email',
            phone: 'Phone number',
            address: 'Address',
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!createTenantDto[field]) {
                throw new BadRequestException(`Missing required field: ${label}`);
            }
        }
    }

    private async checkExistingTenant(name: string): Promise<void> {
        const existingTenant = await this.tenantRepository.findOne({
            where: {name},
        });

        if (existingTenant) {
            throw new ConflictException('A tenant with this name already exists');
        }
    }
}
