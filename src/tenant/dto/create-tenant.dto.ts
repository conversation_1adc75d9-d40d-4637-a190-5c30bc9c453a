import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {IsBoolean, IsDate, IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl,} from 'class-validator';
import {Type} from 'class-transformer';

export class CreateTenantDto {
    @ApiProperty({description: 'Name of the tenant organization or business', example: 'Acme Corporation'})
    @IsString({message: 'Name must be a valid string'})
    @IsNotEmpty({message: 'Name is required'})
    name: string;

    @ApiProperty({description: 'Tenant contact email address', example: '<EMAIL>'})
    @IsEmail({}, {message: 'Email must be a valid email address'})
    @IsNotEmpty({message: 'Email is required'})
    email: string;

    @ApiProperty({description: 'Primary contact phone number for the tenant', example: '+1234567890'})
    @IsString({message: 'Phone must be a valid string'})
    @IsNotEmpty({message: 'Phone number is required'})
    phone: string;

    @ApiProperty({description: 'Physical or mailing address of the tenant', example: '123 Main St, City, Country'})
    @IsString({message: 'Address must be a valid string'})
    @IsNotEmpty({message: 'Address is required'})
    address: string;

    @ApiPropertyOptional({description: 'Tenant website or landing page', example: 'https://acme.com'})
    @IsOptional()
    @IsUrl({}, {message: 'Website must be a valid URL'})
    website?: string;

    @ApiPropertyOptional({description: 'Logo or branding image of the tenant', example: 'https://acme.com/logo.png'})
    @IsOptional()
    @IsUrl({}, {message: 'Logo URL must be a valid URL'})
    logoUrl?: string;

    @ApiPropertyOptional({description: 'Status flag to indicate if the tenant is active', default: true})
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional({description: 'Subscription or billing plan name'})
    @IsOptional()
    @IsString()
    subscriptionPlan?: string;

    @ApiPropertyOptional({description: 'Date when the current subscription started'})
    @IsOptional()
    @Type(() => Date)
    @IsDate()
    subscriptionStartDate?: Date;

    @ApiPropertyOptional({description: 'Date when the current subscription ends'})
    @IsOptional()
    @Type(() => Date)
    @IsDate()
    subscriptionEndDate?: Date;
}
