import {Expose} from "class-transformer";

export class TenantResponseDto {
    @Expose()
    id: string;

    @Expose()
    name: string;

    @Expose()
    code: string;

    @Expose()
    email: string;

    @Expose()
    phone: string;

    @Expose()
    address: string;

    @Expose()
    website?: string;

    @Expose()
    logoUrl?: string;

    @Expose()
    isActive?: boolean;

    @Expose()
    subscriptionPlan?: string;

    @Expose()
    subscriptionStartDate?: Date;

    @Expose()
    subscriptionEndDate?: Date;

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;

    @Expose()
    deletedAt?: Date;
} 