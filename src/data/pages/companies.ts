import api from '../../services/api'
import {Company} from '../../pages/companies/types'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: 'name' | 'updatedAt' | 'createdAt'
    sortingOrder: 'ASC' | 'DESC' | null
}

export const getCompanies = async (options: Partial<Sorting> & Pagination) => {
    const response = await fetch(api.allCompanies(options)).then((r) => r.json())
    return {
        data: response.data || [],
        pagination: response.meta || {page: 1, perPage: 10, total: 0},
    }
}

export const addCompany = async (company: Omit<Company, 'id' | 'createdAt' | 'updatedAt'>) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')

    console.log("company:...........", company)
    const {id, code, isActive, createdAt, updatedAt, ...rawPayload} = company

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.allCompanies(), {
        method: 'POST',
        body: JSON.stringify(payload),
        headers
    }).then((r) => r.json())
    console.log("response:...........", response)
    return response.data
}

export const updateCompany = async (company: Company) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    console.log("company:...........", company)
    const {id, code, createdAt, updatedAt, ...rawPayload} = company

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.company(id), {
        method: 'PATCH',
        body: JSON.stringify(payload),
        headers,
    }).then((r) => r.json())

    console.log("response:...........", response)

    return response.data
}


export const removeCompany = async (company: Company) => {
    const response = await fetch(api.company(company.id), {method: 'DELETE'})
    return response.ok
}
