import axios from 'axios'
import api from '../../services/api'
import authService from '../../services/auth.service'
import type { 
    AuditTrail, 
    AuditQueryParams, 
    PaginatedAuditResult, 
    AuditStatistics 
} from '../../pages/audit/types'

// Get audit logs with filtering and pagination
export const getAuditLogs = async (params: AuditQueryParams = {}): Promise<PaginatedAuditResult> => {
    try {
        const response = await axios.get(api.allAuditLogs(params), {
            headers: {
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })
        return response.data
    } catch (error) {
        console.error('Error fetching audit logs:', error)
        throw error
    }
}

// Get recent audit logs for timeline
export const getRecentAuditLogs = async (limit: number = 10): Promise<AuditTrail[]> => {
    try {
        const response = await axios.get(api.recentAuditLogs(limit), {
            headers: {
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })
        return response.data
    } catch (error) {
        console.error('Error fetching recent audit logs:', error)
        throw error
    }
}

// Get audit statistics
export const getAuditStatistics = async (days: number = 30): Promise<AuditStatistics> => {
    try {
        const response = await axios.get(api.auditStatistics(days), {
            headers: {
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })
        return response.data
    } catch (error) {
        console.error('Error fetching audit statistics:', error)
        throw error
    }
}

// Get single audit log by ID
export const getAuditLog = async (id: string): Promise<AuditTrail> => {
    try {
        const response = await axios.get(api.auditLog(id), {
            headers: {
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })
        return response.data
    } catch (error) {
        console.error('Error fetching audit log:', error)
        throw error
    }
}

// Format relative time for display
export const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
        return `${diffInSeconds}s ago`
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes}m ago`
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours}h ago`
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400)
        return `${days}d ago`
    } else {
        return date.toLocaleDateString()
    }
}

// Format full date for display
export const formatFullDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleString()
}

// Generate description for timeline display
export const generateTimelineDescription = (auditLog: AuditTrail): string => {
    const { action, entityName, entityId, description, userFullName, username } = auditLog
    
    const user = userFullName || username || 'System'
    const entity = entityName || 'item'
    const entityRef = entityId ? ` (ID: ${entityId})` : ''
    
    if (description) {
        return `${user} ${description.toLowerCase()}${entityRef}`
    }
    
    // Generate default description based on action
    switch (action) {
        case 'CREATE':
            return `${user} created a new ${entity}${entityRef}`
        case 'UPDATE':
            return `${user} updated ${entity}${entityRef}`
        case 'DELETE':
            return `${user} deleted ${entity}${entityRef}`
        case 'ACTIVATE':
            return `${user} activated ${entity}${entityRef}`
        case 'DEACTIVATE':
            return `${user} deactivated ${entity}${entityRef}`
        case 'LOGIN':
            return `${user} logged into the system`
        case 'LOGOUT':
            return `${user} logged out of the system`
        case 'VIEW':
            return `${user} viewed ${entity}${entityRef}`
        case 'APPROVE':
            return `${user} approved ${entity}${entityRef}`
        case 'REJECT':
            return `${user} rejected ${entity}${entityRef}`
        case 'SCHEDULE':
            return `${user} scheduled ${entity}${entityRef}`
        case 'COMPLETE':
            return `${user} completed ${entity}${entityRef}`
        case 'CANCEL':
            return `${user} cancelled ${entity}${entityRef}`
        case 'EXPORT':
            return `${user} exported ${entity} data`
        case 'IMPORT':
            return `${user} imported ${entity} data`
        default:
            return `${user} performed ${action.toLowerCase()} on ${entity}${entityRef}`
    }
}
