import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query} from '@nestjs/common';
import {VehicleTypeService} from './vehicle-type.service';
import {CreateVehicleTypeDto} from './dto/create-vehicle-type.dto';
import {UpdateVehicleTypeDto} from './dto/update-vehicle-type.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {VehicleType} from "./entities/vehicle-type.entity";

@ApiTags('Vehicle Type')
@Controller('vehicle-type')
export class VehicleTypeController {
    constructor(private readonly vehicleTypeService: VehicleTypeService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new vehicle type'})
    @ApiResponse({status: 201, description: 'The vehicle type has been successfully created.', type: VehicleType})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Vehicle type already exists.'})
    create(@Body() createVehicleTypeDto: CreateVehicleTypeDto) {
        return this.vehicleTypeService.create(createVehicleTypeDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Type'})
    @ApiResponse({
        status: 200,
        description: 'The vehicle type retrieved successfully.',
        type: VehicleType,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle type with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.vehicleTypeService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve vehicle type by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle type ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle type retrieved successfully.',
        type: VehicleType,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle type with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.vehicleTypeService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update vehicle type'})
    @ApiParam({
        name: 'identifier',
        description: 'Vehicle type ID (UUID) or name',
        type: String,
        example: 'Toyota' // or 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle type successfully updated.',
        type: VehicleType
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle type with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Vehicle type with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateVehicleTypeDto: UpdateVehicleTypeDto
    ) {
        return this.vehicleTypeService.update(identifier, updateVehicleTypeDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete vehicle type'})
    @ApiParam({
        name: 'id',
        description: 'Vehicle type ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The vehicle type successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Vehicle type with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.vehicleTypeService.remove(id);
    }

}
