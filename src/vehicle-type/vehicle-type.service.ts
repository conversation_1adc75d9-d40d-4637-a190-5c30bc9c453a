import {ConflictException, Injectable, NotFoundException} from '@nestjs/common';
import {CreateVehicleTypeDto} from './dto/create-vehicle-type.dto';
import {VehicleTypeResponseDto} from "./dto/vehicle-type-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {VehicleType} from "./entities/vehicle-type.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateVehicleTypeDto} from "./dto/update-vehicle-type.dto";

@Injectable()
export class VehicleTypeService {
    constructor(
        @InjectRepository(VehicleType) private readonly vehicleTypeRepository: Repository<VehicleType>,
    ) {
    }

    async create(createVehicleTypeDto: CreateVehicleTypeDto): Promise<{
        data: VehicleTypeResponseDto;
        message: string
    }> {
        const existingVehicleType = await this.vehicleTypeRepository.findOne({
            where: {name: createVehicleTypeDto.name},
        });

        if (existingVehicleType) {
            throw new ConflictException('Vehicle type with this name already exists');
        }

        const vehicleType = this.vehicleTypeRepository.create({...createVehicleTypeDto});
        const savedVehicleType = await this.vehicleTypeRepository.save(vehicleType);

        return {
            data: plainToInstance(VehicleTypeResponseDto, savedVehicleType, {
                excludeExtraneousValues: true,
            }),
            message: 'The vehicle type successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: VehicleTypeResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.vehicleTypeRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(VehicleTypeResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Vehicle types retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: VehicleTypeResponseDto;
        message: string;
    }> {
        let vehicleType: VehicleType | null = null;

        if (isUUID(query)) {
            vehicleType = await this.vehicleTypeRepository.findOne({
                where: {id: query},
            });
        }

        if (!vehicleType) {
            vehicleType = await this.vehicleTypeRepository.findOne({
                where: {name: query},
            });
        }

        if (!vehicleType) {
            throw new NotFoundException(
                `Vehicle type with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(VehicleTypeResponseDto, vehicleType, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle type retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateVehicleTypeDto: UpdateVehicleTypeDto,
    ): Promise<{ data: VehicleTypeResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const vehicleType = await this.vehicleTypeRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!vehicleType) {
            throw new NotFoundException('Vehicle type with ID or name not found.');
        }

        if (
            updateVehicleTypeDto.name &&
            updateVehicleTypeDto.name !== vehicleType.name
        ) {
            const existing = await this.vehicleTypeRepository.findOne({
                where: {name: updateVehicleTypeDto.name},
            });

            if (existing) {
                throw new ConflictException('Vehicle type with the provided name already exists.');
            }
        }

        Object.assign(vehicleType, updateVehicleTypeDto);
        const updated = await this.vehicleTypeRepository.save(vehicleType);

        const data = plainToInstance(VehicleTypeResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The vehicle type successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const vehicleType = await this.vehicleTypeRepository.findOne({where: {id}});

        if (!vehicleType) {
            throw new NotFoundException(`Vehicle type with id ${id} not found`);
        }

        await this.vehicleTypeRepository.remove(vehicleType);

        return {message: 'The vehicle type successfully deleted.'};
    }
}
