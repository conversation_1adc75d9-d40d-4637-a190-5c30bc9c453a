import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Vehicle} from "../../vehicles/entities/vehicle.entity";

@Entity('vehicle_type')
export class VehicleType extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the vehicle name'})
    name: string;

    @OneToMany(() => Vehicle, (vehicle) => vehicle.vehicleType)
    @ApiProperty({description: 'Vehicles of this type', type: () => [Vehicle]})
    vehicles: Vehicle[];
}
