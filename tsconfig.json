{"compilerOptions": {"noEmit": true, "target": "esnext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "bundler", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "types": ["vite/client"], "allowJs": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "vite.config.ts", "node_modules/vuestic-ui"], "exclude": ["node_modules/**/*"]}