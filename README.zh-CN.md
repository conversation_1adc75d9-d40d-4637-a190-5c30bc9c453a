<p align="center">
  <a href="https://vuestic.dev" target="_blank">
    <img alt="Vuestic UI Logo" width="220" src="./.github/assets/vuestic-admin-logo.png">
  </a>
</p>

<p align="center">
  免费且美观的管理模板，使用Vue 3、Vite、Pinia和Tailwind CSS构建。设计用于构建高效、响应式和快速加载的管理界面。</br>
  由<a href="https://epicmax.co">Epicmax</a>开发。</br>
  基于<a href="https://ui.vuestic.dev">Vuestic UI</a>库。
</p>

<p align="center">
  <a href="https://admin-demo.vuestic.dev"> 在线演示 </a> |
  <a href="https://admin-landing.vuestic.dev/"> 关于Vuestic Admin </a> |
  <a href="https://ui.vuestic.dev/">Vuestic UI文档</a>
</p>

> Vuestic Admin是使用[Vuestic UI](https://ui.vuestic.dev)构建的。查看我们的
> <a href="https://github.com/epicmaxco/vuestic-ui/issues">问题</a>，
> <a href="https://ui.vuestic.dev/en/contribution/guide">贡献指南</a> 并参与我们的
> <a href="https://discord.gg/jTKTjj2weV">Discord服务器</a>，帮助我们改进Vuestic Admin和Vuestic UI体验。

<p align="center">
  <a href="https://admin.vuestic.dev" target="_blank">
    <img src="./public/vuestic-admin-image.png" align="center" width="888px"/>
  </a>
</p>

### 快速入门

使用以下命令快速搭建新的[Vuestic Admin](admin-demo.vuestic.ui)或使用[Vuestic UI](ui.vuestic.dev)的空白Vite或Nuxt项目。

```bash
npm create vuestic@latest
```

安装[Vuestic Admin](admin.vuestic.ui)后，运行 `npm install` 安装依赖，然后运行 `npm run dev` 启动本地开发服务器。

### 文档

文档、指南、示例和教程可在[ui.vuestic.dev](https://ui.vuestic.dev)上找到。

### 官方Discord服务器

在官方社区的 [discord服务器](https://discord.gg/jTKTjj2weV)上提问。

### 特性

- **Vue 3、Vite、Pinia和Tailwind CSS -** 快速高效的开发
- **深色主题 -** 现代且引人注目
- **全局配置 -** 轻松定制
- **可访问性 -** 包容且用户友好
- **i18n集成 -** 便于全球本地化
- **教育资源 -** 适用于学习和提高技能
- **响应式设计 -** 无缝适应所有设备
- **专业支持 -** 专家提供可靠帮助
- **高度可定制 -** 可根据项目风格定制

### 贡献

感谢您所有出色的PR、问题和想法。

<a href="https://github.com/epicmaxco/vuestic-admin/graphs/contributors">
<img src="https://opencollective.com/vuestic-admin/contributors.svg?width=890&button=false" />
</a>
<br>

欢迎随时加入：查看我们的<a href="https://ui.vuestic.dev/en/contribution/guide">贡献指南</a>，[开放问题](https://github.com/epicmaxco/vuestic-ui/issues)和[Discord服务器](https://discord.gg/jTKTjj2weV)。

### 合作伙伴与赞助商 ❤️

<img src="./.github/assets/sponsors.png" loading="lazy" alt="Epicmax, vuejobs, ag-grid, flatlogic, browserstack and jetbrains" width="400px">

成为合作伙伴：[<EMAIL>](mailto:<EMAIL>)

### 我能雇佣你们吗？

[Epicmax](https://epicmax.co) 从一开始就致力于开源。Vuestic Admin是由Epicmax创建并支持的，经过多年的支持。

在前端开发方面，Epicmax在商业和开源项目上已经有超过6年的专业经验，与全球各个领域的47个以上的客户合作。我们定期对我们的项目进行代码审核，现在很高兴不仅向我们现有的客户提供此服务，而且向任何希望了解其前端代码状态并确保其安全和最新的人提供此服务！

您可以通过[Epicmax的这个表单](https://epicmax.co/contacts)请求咨询或订购Web开发服务 😎

打个招呼：<a href="mailto:<EMAIL>"><EMAIL></a>。我们将很高兴与您合作！

[我们做过的其他工作](https://epicmax.co) 🤘

[认识团队](https://ui.vuestic.dev/introduction/team)

### 奖项

<a href="https://flatlogic.com/templates/vuestic-vue-free-admin" target="_blank">
    <img src="https://i.imgur.com/ZeQPZ3Q.png" align="center" width="150px"/>
</a>
<p>
  由<a href="https://flatlogic.com/templates/vuestic-vue-free-admin" target="_blank">@flatlogic</a>市场提供
</p>

### 关注我们

随时关注最新的Vuestic新闻！在[Twitter上](https://twitter.com/vuestic_ui)
或[Linkedin上](
