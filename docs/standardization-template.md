# Module Standardization Template

This template shows the standardization pattern that should be applied to all modules based on the tenant module implementation.

## Service Template

```typescript
import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { validate as isUUID } from 'uuid';
import { plainToInstance } from "class-transformer";
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class [ModuleName]Service {
    private readonly logger = new Logger([ModuleName]Service.name);

    constructor(
        @InjectRepository([Entity]) private readonly [entity]Repository: Repository<[Entity]>,
    ) {}

    async create(create[ModuleName]Dto: Create[ModuleName]Dto): Promise<ApiResponse<[ModuleName]ResponseDto>> {
        this.logger.log(`Creating new [entity]: ${create[ModuleName]Dto.name}`);
        
        try {
            // Validate required fields
            this.validateRequiredFields(create[ModuleName]Dto);

            // Check for existing entity
            await this.checkExisting[ModuleName](create[ModuleName]Dto.name);

            const [entity] = this.[entity]Repository.create({...create[ModuleName]Dto});
            
            let saved[ModuleName];
            try {
                saved[ModuleName] = await this.[entity]Repository.save([entity]);
            } catch (error) {
                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                // Unique violation
                if (error.code === '23505') {
                    throw new ConflictException('A [entity] with this information already exists');
                }

                this.logger.error('Unexpected error while saving [entity]:', error);
                throw new InternalServerErrorException('Unexpected error while creating [entity].');
            }

            const responseData = plainToInstance([ModuleName]ResponseDto, saved[ModuleName], {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully created [entity]: ${saved[ModuleName].name}`);

            return ResponseUtil.success(
                responseData,
                '[ModuleName] created successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to create [entity]: ${error.message}`, error.stack);
            throw error;
        }
    }

    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<PaginatedApiResponse<[ModuleName]ResponseDto[]>> {
        this.logger.log(`Fetching [entities] - Page: ${page}, PerPage: ${perPage}, Sort: ${sort}, Order: ${order}`);
        try {
            const [records, total] = await this.[entity]Repository.findAndCount({
                order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
                skip: (page - 1) * perPage,
                take: perPage,
            });

            const data = plainToInstance([ModuleName]ResponseDto, records, {
                excludeExtraneousValues: true,
            });

            const meta = {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            };

            this.logger.log(`Successfully retrieved ${records.length} [entities]`);

            return ResponseUtil.paginated(
                data,
                meta,
                '[ModuleName]s retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to retrieve [entities]: ${error.message}`, error.stack);
            throw new InternalServerErrorException('Failed to retrieve [entities]');
        }
    }

    async findOneByIdOrName(query: string): Promise<ApiResponse<[ModuleName]ResponseDto>> {
        this.logger.log(`Finding [entity] by ID or name: ${query}`);
        try {
            if (!query) {
                throw new BadRequestException('Query parameter is required');
            }

            const isUuid = isUUID(query);

            const [entity] = await this.[entity]Repository.findOne({
                where: isUuid ? {id: query} : {name: query},
            });

            if (![entity]) {
                throw new NotFoundException('[ModuleName] with ID or name not found.');
            }

            const data = plainToInstance([ModuleName]ResponseDto, [entity], {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully found [entity]: ${[entity].name}`);

            return ResponseUtil.success(
                data,
                '[ModuleName] retrieved successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to find [entity]: ${error.message}`, error.stack);
            throw error;
        }
    }

    async update(
        identifier: string,
        update[ModuleName]Dto: Update[ModuleName]Dto,
    ): Promise<ApiResponse<[ModuleName]ResponseDto>> {
        this.logger.log(`Updating [entity]: ${identifier}`);
        try {
            if (!identifier) {
                throw new BadRequestException('Identifier is required');
            }

            const isUuid = isUUID(identifier);

            const [entity] = await this.[entity]Repository.findOne({
                where: isUuid ? {id: identifier} : {name: identifier},
            });

            if (![entity]) {
                throw new NotFoundException('[ModuleName] with ID or name not found.');
            }

            // Check for conflicts if updating name
            if (update[ModuleName]Dto.name && update[ModuleName]Dto.name !== [entity].name) {
                const existing = await this.[entity]Repository.findOne({
                    where: {name: update[ModuleName]Dto.name},
                });

                if (existing) {
                    throw new ConflictException('[ModuleName] with the provided name already exists.');
                }
            }

            Object.assign([entity], update[ModuleName]Dto);
            
            let updated;
            try {
                updated = await this.[entity]Repository.save([entity]);
            } catch (error) {
                if (error.code === '23505') {
                    throw new ConflictException('A [entity] with this information already exists');
                }
                this.logger.error('Unexpected error while updating [entity]:', error);
                throw new InternalServerErrorException('Unexpected error while updating [entity].');
            }

            const data = plainToInstance([ModuleName]ResponseDto, updated, {
                excludeExtraneousValues: true,
            });

            this.logger.log(`Successfully updated [entity]: ${updated.name}`);

            return ResponseUtil.success(
                data,
                '[ModuleName] updated successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to update [entity]: ${error.message}`, error.stack);
            throw error;
        }
    }

    async remove(id: string): Promise<ApiResponse<null>> {
        this.logger.log(`Removing [entity]: ${id}`);
        try {
            if (!id) {
                throw new BadRequestException('ID is required');
            }

            if (!isUUID(id)) {
                throw new BadRequestException('Invalid ID format');
            }

            const [entity] = await this.[entity]Repository.findOne({where: {id}});

            if (![entity]) {
                throw new NotFoundException(`[ModuleName] with id ${id} not found`);
            }

            await this.[entity]Repository.remove([entity]);

            this.logger.log(`Successfully removed [entity]: ${[entity].name}`);

            return ResponseUtil.success(
                null,
                '[ModuleName] deleted successfully'
            );
        } catch (error) {
            this.logger.error(`Failed to remove [entity]: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Helper methods
    private validateRequiredFields(create[ModuleName]Dto: Create[ModuleName]Dto): void {
        const requiredFields: Record<string, string> = {
            name: 'Name',
            // Add other required fields as needed
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!create[ModuleName]Dto[field]) {
                throw new BadRequestException(`Missing required field: ${label}`);
            }
        }
    }

    private async checkExisting[ModuleName](name: string): Promise<void> {
        const existing[ModuleName] = await this.[entity]Repository.findOne({
            where: {name},
        });

        if (existing[ModuleName]) {
            throw new ConflictException('A [entity] with this name already exists');
        }
    }
}
```

## Controller Template

```typescript
import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseFilters, Logger} from '@nestjs/common';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import { HttpExceptionFilter } from '../common/filters/http-exception.filter';

@ApiTags('[ModuleName]')
@Controller('[module-name]')
@UseFilters(HttpExceptionFilter)
export class [ModuleName]Controller {
    private readonly logger = new Logger([ModuleName]Controller.name);

    constructor(private readonly [moduleName]Service: [ModuleName]Service) {}

    // Standard CRUD operations with proper API documentation
}
```

## Modules to Apply This Template To:

1. ✅ Client (Completed)
2. 🔄 Company (In Progress)
3. 🔄 Vehicle Make (In Progress)
4. ⏳ Users
5. ⏳ Valuations
6. ⏳ Vehicle Body Type
7. ⏳ Vehicle Fuel Type
8. ⏳ Vehicle Lighting
9. ⏳ Vehicle Model
10. ⏳ Vehicle Transmission
11. ⏳ Vehicle Type

## Key Benefits:

- **Consistent Error Handling**: All modules handle database errors uniformly
- **Comprehensive Logging**: Detailed logs for debugging and monitoring
- **Standardized Responses**: Consistent API response format across all endpoints
- **Input Validation**: Proper validation with meaningful error messages
- **Type Safety**: Full TypeScript support with proper typing
- **Maintainability**: Consistent code structure makes maintenance easier
