# Client Form Testing Guide

## Issues Fixed

### 1. **Company Name Field Not Showing**
**Problem**: When selecting "Company" type, the company name input field was not appearing.

**Root Cause**: The conditional rendering was using enum comparison (`ClientType.COMPANY`) instead of string comparison.

**Solution**: Changed all conditional checks to use string literals:
```vue
<!-- Before -->
<div v-if="newClient.clientType === ClientType.COMPANY">

<!-- After -->
<div v-if="newClient.clientType === 'COMPANY'">
```

### 2. **VaSelect Configuration**
**Problem**: VaSelect component might not have been properly configured for the options.

**Solution**: Added explicit `text-by` and `value-by` properties:
```vue
<VaSelect
    v-model="newClient.clientType"
    label="Client Type"
    :options="clientTypeOptions"
    text-by="text"
    value-by="value"
    :rules="[required]"
    :readonly="props.readonly"
/>
```

### 3. **CSS Warning Fix**
**Problem**: CSS warning about `align-items: start` should be `flex-start`.

**Solution**: Added CSS override in `src/scss/main.scss`:
```css
/* Fix for CSS warning: align-items: start should be flex-start */
.number-option {
  align-items: flex-start !important;
}

/* Global fix for any align-items: start usage */
[style*="align-items: start"] {
  align-items: flex-start !important;
}
```

## Testing Steps

### 1. **Test Company Client Creation**
1. Open the client form
2. Select "Company" from the Client Type dropdown
3. **Verify**: Company Information section appears with:
   - Company Name field (required)
   - Company Registration Number (optional)
   - Contact Person Name (optional)
   - Contact Person Designation (optional)
4. Fill in required fields:
   - Company Name: "Test Company Ltd"
   - Email: "<EMAIL>"
   - Phone: "+1234567890"
   - Address: "123 Business St"
   - City: "Business City"
   - Country: "Business Country"
   - Postal Code: "12345"
5. Click Save
6. **Expected**: Client should be created successfully

### 2. **Test Individual Client Creation**
1. Open the client form
2. Select "Individual" from the Client Type dropdown
3. **Verify**: Personal Information section appears with:
   - First Name field (required)
   - Middle Name (optional)
   - Last Name field (required)
   - National ID field (required)
4. Fill in required fields:
   - First Name: "John"
   - Last Name: "Doe"
   - National ID: "ID123456789"
   - Email: "<EMAIL>"
   - Phone: "+1234567890"
   - Address: "123 Home St"
   - City: "Home City"
   - Country: "Home Country"
   - Postal Code: "12345"
5. Click Save
6. **Expected**: Client should be created successfully

### 3. **Test Validation**
1. Try to save without filling required fields
2. **Expected**: Validation errors should appear
3. Try to create duplicate clients
4. **Expected**: Appropriate error messages should appear

### 4. **Test UI Display**
1. **Table View**: Verify client type badges and names display correctly
2. **Card View**: Verify client information shows appropriately based on type
3. **Edit Mode**: Verify existing clients can be edited with correct field visibility

## Debug Information

The form now includes debug information that shows the current client type:
```
Current client type: COMPANY
```

This helps verify that the reactive data is working correctly.

## Common Issues and Solutions

### Issue: Fields still not showing
**Solution**: Check browser console for JavaScript errors and verify that:
1. The ClientType enum is properly imported
2. The reactive data is updating correctly
3. No TypeScript compilation errors

### Issue: Form validation not working
**Solution**: Verify that:
1. The validation functions are using string comparison
2. The VaForm validation is properly configured
3. Required fields are marked correctly

### Issue: Save button not working
**Solution**: Check that:
1. The handleSave function is properly defined
2. The API endpoints are accessible
3. The data structure matches the backend expectations

## Next Steps

1. **Test in Development Environment**: Run `npm run dev` and test the form
2. **Check Network Requests**: Use browser dev tools to verify API calls
3. **Test Error Handling**: Verify error messages display correctly
4. **Cross-browser Testing**: Test in different browsers
5. **Mobile Testing**: Verify responsive design works on mobile devices

The implementation should now work correctly with proper field visibility based on client type and successful client creation/editing.
