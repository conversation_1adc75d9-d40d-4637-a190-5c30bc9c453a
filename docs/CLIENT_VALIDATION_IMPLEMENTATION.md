# Client Validation Implementation

## Overview
This document describes the implementation of client type-based validation for the client service. The system now supports two types of clients: **COMPANY** and **INDIVIDUAL**, with different validation rules for each type.

## Changes Made

### 1. Updated CreateClientDto (`src/client/dto/create-client.dto.ts`)

**Key Changes:**
- Added conditional validation using `@ValidateIf` decorator
- Made fields mandatory based on client type:
  - **COMPANY type**: `companyName` is required
  - **INDIVIDUAL type**: `firstName`, `lastName`, and `nationalId` are required
- `middleName` remains optional for individuals
- All other fields (email, phone, address, etc.) remain required for both types

**Validation Rules:**
```typescript
// For COMPANY clients
@ValidateIf(o => o.clientType === ClientType.COMPANY)
@IsNotEmpty({message: 'Company name is required for company clients'})
companyName?: string;

// For INDIVIDUAL clients
@ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
@IsNotEmpty({message: 'First name is required for individual clients'})
firstName?: string;

@ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
@IsNotEmpty({message: 'Last name is required for individual clients'})
lastName?: string;

@ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
@IsNotEmpty({message: 'National ID is required for individual clients'})
nationalId?: string;
```

### 2. Updated ClientService (`src/client/client.service.ts`)

**Key Changes:**
- Removed the old `validateRequiredFields` method
- Updated `checkExistingClient` method to handle client type-specific validation
- Updated all logging to show appropriate client identifiers
- Enhanced duplicate checking logic

**Client Type-Specific Duplicate Checking:**

#### For COMPANY clients:
- Checks if a company with the same `companyName` already exists
- Always checks for duplicate `email` and `phone` regardless of client type

#### For INDIVIDUAL clients:
- Checks if an individual with the same combination of:
  - `firstName`
  - `lastName` 
  - `nationalId`
  - `middleName` (if provided)
- Always checks for duplicate `email` and `phone` regardless of client type

**Error Messages:**
- `"A company with this name already exists"` - for duplicate company names
- `"An individual with this name and national ID already exists"` - for duplicate individuals
- `"A client with this email already exists"` - for duplicate emails
- `"A client with this phone number already exists"` - for duplicate phone numbers

### 3. Enhanced Search and Update Logic

**Search by Name:**
- For companies: searches by `companyName`
- For individuals: searches by `firstName + lastName` combination

**Update Validation:**
- Prevents updates that would create duplicates
- Maintains client type-specific validation rules
- Checks for conflicts before saving changes

### 4. Added Comprehensive Tests (`src/client/client.service.spec.ts`)

**Test Coverage:**
- ✅ Creating company clients successfully
- ✅ Creating individual clients successfully  
- ✅ Preventing duplicate company names
- ✅ Preventing duplicate individual details (name + national ID)
- ✅ Preventing duplicate emails across all client types
- ✅ Preventing duplicate phone numbers across all client types

## Usage Examples

### Creating a Company Client
```json
{
  "clientType": "COMPANY",
  "companyName": "Acme Corporation Ltd",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": "123 Business Street",
  "city": "Business City",
  "country": "Business Country",
  "postalCode": "12345"
}
```

### Creating an Individual Client
```json
{
  "clientType": "INDIVIDUAL", 
  "firstName": "John",
  "lastName": "Doe",
  "nationalId": "ID123456789",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": "123 Home Street",
  "city": "Home City", 
  "country": "Home Country",
  "postalCode": "12345"
}
```

## Benefits

1. **Type Safety**: Clear separation between company and individual clients
2. **Flexible Validation**: Different required fields based on client type
3. **Duplicate Prevention**: Intelligent duplicate checking based on client type
4. **Better UX**: Clear error messages indicating what fields are required
5. **Maintainable Code**: Clean separation of concerns and comprehensive test coverage

## Future Enhancements

- Add validation for company registration numbers
- Implement more sophisticated name matching for individuals
- Add support for additional client types if needed
- Enhance search functionality with fuzzy matching
