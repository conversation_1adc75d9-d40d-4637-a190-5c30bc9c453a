import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import {dirname, resolve} from 'node:path'
import {fileURLToPath} from 'url'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import {vuestic} from '@vuestic/compiler/vite'

// https://vitejs.dev/config/
export default defineConfig({
    build: {
        sourcemap: true,
    },
    plugins: [
        vuestic({
            devtools: true,
            cssLayers: true,
        }),
        vue(),
        VueI18nPlugin({
            include: resolve(dirname(fileURLToPath(import.meta.url)), './src/i18n/locales/**'),
        }),
    ],
    server: {
        host: true, // or '0.0.0.0'
        port: 5173, // optional, defaults to 5173
    },
})
