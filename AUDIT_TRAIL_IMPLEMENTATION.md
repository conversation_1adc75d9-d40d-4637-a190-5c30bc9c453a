# Audit Trail Implementation

This document describes the comprehensive audit trail system implemented to track user activity across all backend modules.

## Overview

The audit trail system automatically tracks and logs all user actions performed in the application, providing a complete history of changes for compliance, security, and debugging purposes.

## Backend Architecture

### Core Components

1. **AuditTrail Entity** (`src/audit/entities/audit-trail.entity.ts`)
   - Stores audit log entries with comprehensive metadata
   - Supports JSON fields for old/new values
   - Links to user entities with soft references

2. **AuditService** (`src/audit/audit.service.ts`)
   - Handles audit log creation and querying
   - Provides pagination and filtering capabilities
   - Generates statistics and reports

3. **AuditController** (`src/audit/audit.controller.ts`)
   - REST API endpoints for accessing audit data
   - Supports filtering, pagination, and search

4. **AuditInterceptor** (`src/audit/interceptors/audit.interceptor.ts`)
   - Automatically captures HTTP requests/responses
   - Extracts user information and metadata
   - Applied globally to all controllers

### Audit Actions Supported

- `CREATE` - Entity creation
- `UPDATE` - Entity modification
- `DELETE` - Entity deletion
- `LOGIN` - User authentication
- `LOGOUT` - User session termination
- `ACTIVATE` - Entity activation
- `DEACTIVATE` - Entity deactivation
- `APPROVE` - Approval actions
- `REJECT` - Rejection actions
- `SCHEDULE` - Scheduling actions
- `COMPLETE` - Completion actions
- `CANCEL` - Cancellation actions
- `VIEW` - Entity viewing (optional)
- `EXPORT` - Data export
- `IMPORT` - Data import

### Modules Tracked

- USER - User management
- CLIENT - Client management
- COMPANY - Company management
- TENANT - Tenant management
- VALUATION - Valuation processes
- VEHICLE - Vehicle management
- VEHICLE_* - Vehicle-related entities
- ROLE - Role management
- PERMISSION - Permission management
- AUTH - Authentication events
- SYSTEM - System-level events

## Usage

### Adding Audit Logging to Controllers

Use the `@Audit` decorator on controller methods:

```typescript
import { Audit } from '../audit/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../audit/entities/audit-trail.entity';

@Post()
@Audit({
  module: AuditModule.USER,
  action: AuditAction.CREATE,
  entityName: 'User',
  description: 'Created a new user'
})
create(@Body() createUserDto: CreateUserDto) {
  return this.usersService.create(createUserDto);
}
```

### Manual Audit Logging

For custom audit logging in services:

```typescript
import { AuditService } from '../audit/audit.service';

constructor(private readonly auditService: AuditService) {}

async someMethod() {
  await this.auditService.log({
    module: AuditModule.SYSTEM,
    action: AuditAction.COMPLETE,
    entityName: 'BatchProcess',
    description: 'Completed batch processing',
    metadata: JSON.stringify({ processedCount: 100 })
  });
}
```

## API Endpoints

### Get Audit Logs
```
GET /api/v1/audit
Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- module: AuditModule
- action: AuditAction
- userId: string
- username: string
- startDate: ISO string
- endDate: ISO string
- search: string
```

### Get Recent Logs
```
GET /api/v1/audit/recent?limit=10
```

### Get Statistics
```
GET /api/v1/audit/statistics?days=30
```

### Get Single Audit Log
```
GET /api/v1/audit/:id
```

## Frontend Integration

### Timeline Component

The Timeline component (`src/pages/admin/dashboard/cards/Timeline.vue`) displays recent audit activity:

- Fetches recent audit logs from the API
- Displays user-friendly descriptions
- Shows relative timestamps
- Handles loading and error states

### Audit Logs Page

A comprehensive audit logs page (`src/pages/audit/AuditLogs.vue`) provides:

- Advanced filtering and search
- Pagination
- Detailed view modal
- Export functionality
- Real-time updates

### Data Services

Frontend data services (`src/data/pages/audit.ts`) provide:

- API integration functions
- Data formatting utilities
- Error handling
- Type-safe interfaces

## Database Schema

The audit_trail table includes:

- `id` - Primary key (UUID)
- `module` - Enum of tracked modules
- `action` - Enum of audit actions
- `entity_id` - ID of affected entity
- `entity_name` - Name/type of affected entity
- `description` - Human-readable description
- `old_values` - JSONB of previous values
- `new_values` - JSONB of new values
- `ip_address` - Client IP address
- `user_agent` - Client user agent
- `metadata` - Additional JSON metadata
- `user_id` - Reference to user (nullable)
- `username` - Username for display
- `user_full_name` - Full name for display
- `created_at` - Timestamp
- `updated_at` - Timestamp
- `deleted_at` - Soft delete timestamp

## Security Considerations

1. **Data Sanitization** - Sensitive fields (passwords, tokens) are automatically redacted
2. **Soft References** - User references use soft foreign keys to prevent data loss
3. **Error Handling** - Audit failures don't break main operations
4. **Access Control** - Audit endpoints should be protected with appropriate permissions

## Performance Considerations

1. **Async Logging** - Audit logging is non-blocking
2. **Indexing** - Database indexes on commonly queried fields
3. **Pagination** - All list endpoints support pagination
4. **Archiving** - Consider implementing data archiving for old audit logs

## Testing

Unit tests are provided for the AuditService (`src/audit/audit.service.spec.ts`) covering:

- Audit log creation
- Error handling
- Querying and filtering
- Statistics generation

## Future Enhancements

1. **Real-time Updates** - WebSocket integration for live audit feeds
2. **Advanced Analytics** - Dashboards and reporting
3. **Data Retention** - Automated archiving and cleanup
4. **Compliance Reports** - Pre-built compliance reporting
5. **Alerting** - Suspicious activity detection and alerts
