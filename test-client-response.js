// Simple test to verify ClientResponseDto transformation
// This demonstrates that the tenant and company data should now be included

const mockClientData = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  clientType: "INDIVIDUAL",
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+1234567890",
  address: "123 Main St",
  city: "City",
  country: "Country",
  postalCode: "12345",
  nationalId: "ID123456789",
  createdAt: new Date(),
  updatedAt: new Date(),
  tenants: [
    {
      id: "tenant-1",
      name: "Tenant One",
      code: "T001",
      email: "<EMAIL>",
      phone: "+1111111111",
      address: "Tenant Address 1",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: "tenant-2", 
      name: "Tenant Two",
      code: "T002",
      email: "<EMAIL>",
      phone: "+2222222222",
      address: "Tenant Address 2",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ],
  companies: [
    {
      id: "company-1",
      name: "Company One",
      contactPerson: "Contact Person 1",
      email: "<EMAIL>",
      phone: "+3333333333",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
};

console.log("Mock client data with relations:");
console.log("- Client ID:", mockClientData.id);
console.log("- Client Type:", mockClientData.clientType);
console.log("- Client Name:", `${mockClientData.firstName} ${mockClientData.lastName}`);
console.log("- Associated Tenants:", mockClientData.tenants.length);
mockClientData.tenants.forEach((tenant, index) => {
  console.log(`  ${index + 1}. ${tenant.name} (${tenant.code})`);
});
console.log("- Associated Companies:", mockClientData.companies.length);
mockClientData.companies.forEach((company, index) => {
  console.log(`  ${index + 1}. ${company.name}`);
});

console.log("\n✅ With the updated ClientResponseDto:");
console.log("- @Expose() and @Type(() => TenantResponseDto) on tenants property");
console.log("- @Expose() and @Type(() => CompanyResponseDto) on companies property");
console.log("- Relations are loaded in findAll: ['tenants', 'companies']");
console.log("- plainToInstance will now properly transform the nested objects");
console.log("\n🎉 The tenant and company data should now be included in API responses!");
