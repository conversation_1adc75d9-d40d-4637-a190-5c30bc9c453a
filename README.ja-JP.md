<p align="center">
  <a href="https://vuestic.dev" target="_blank">
    <img alt="Vuestic UI ロゴ" width="220" src="./.github/assets/vuestic-admin-logo.png">
  </a>
</p>

<p align="center">
  Vue 3、Vite、Pinia、およびTailwind CSS を利用した無料で美しい管理テンプレート。効率的でレスポンシブ、かつ高速な管理インターフェースの構築に最適です。</br>
  開発者: <a href="https://epicmax.co">Epicmax</a>。</br>
  <a href="https://ui.vuestic.dev">Vuestic UI</a> ライブラリをベースにしています。
</p>

<p align="center">
  <a href="https://admin-demo.vuestic.dev"> ライブデモ </a> |
  <a href="https://admin-landing.vuestic.dev/"> Vuestic Admin について </a> |
  <a href="https://ui.vuestic.dev/">Vuestic UI ドキュメンテーション</a>
</p>

> Vuestic Admin は [Vuestic UI](https://ui.vuestic.dev) で構築されています。私たちの
> <a href="https://github.com/epicmaxco/vuestic-ui/issues">課題</a>、
> <a href="https://ui.vuestic.dev/en/contribution/guide">貢献ガイド</a> を参照して、
> <a href="https://discord.gg/jTKTjj2weV">Discord サーバー</a> でディスカッションに参加して、Vuestic Admin ＆ Vuestic UI の体験を向上させるのに役立ててください。

<p align="center">
  <a href="https://admin.vuestic.dev" target="_blank">
    <img src="./public/vuestic-admin-image.png" align="center" width="888px"/>
  </a>
</p>

### クイックスタート

次のコマンドを使用して、[Vuestic Admin](admin-demo.vuestic.ui) または空の Vite または Nuxt プロジェクトを [Vuestic UI](ui.vuestic.dev) と共に素早く構築します。

```bash
npm create vuestic@latest
```

[Vuestic Admin](admin.vuestic.ui) をインストールしたら、`npm install` を実行して依存関係をインストールし、次に `npm run dev` を実行してローカル開発サーバーを起動します。

### ドキュメンテーション

ドキュメンテーション、ガイド、例、およびチュートリアルは [ui.vuestic.dev](https://ui.vuestic.dev) で利用可能です。

### 公式 Discord サーバー

公式コミュニティの [Discord サーバー](https://discord.gg/jTKTjj2weV) で質問してください。

### 特徴

- **Vue 3、Vite、Pinia、および Tailwind CSS -** 高速かつ効率的な開発
- **ダークテーマ -** モダンで目を引く
- **グローバル構成 -** 無駄なくカスタマイズ可能
- **アクセシビリティ -** 包括的でユーザーフレンドリー
- **i18n 統合 -** グローバルな展開のための簡単なローカリゼーション
- **教育リソース -** 学習とスキル向上に最適
- **レスポンシブデザイン -** すべてのデバイスにシームレスに適応
- **プロフェッショナルサポート -** 専門家からの信頼性のあるサポート
- **高度にカスタマイズ可能 -** プロジェクトのスタイルに合わせて調整

### 貢献

素晴らしい PR、課題、アイデアに感謝します。

<a href="https://github.com/epicmaxco/vuestic-admin/graphs/contributors">
<img src="https://opencollective.com/vuestic-admin/contributors.svg?width=890&button=false" />
</a>
<br>

いつでも参加歓迎です：私たちの
<a href="https://ui.vuestic.dev/en/contribution/guide">
貢献ガイド</a>
、 [オープン課題](https://github.com/epicmaxco/vuestic-ui/issues)
および [Discord サーバー](https://discord.gg/jTKTjj2weV) を確認してください。

### パートナー & スポンサー ❤️

<img src="./.github/assets/sponsors.png" loading="lazy" alt="Epicmax、vuejobs、ag-grid、flatlogic、browserstack、jetbrains" width="400px">

パートナーになる: [<EMAIL>](mailto:<EMAIL>)

### お仕事の依頼はできますか？

[Epicmax](https://epicmax.co) は初めからオープンソースにコミットしています。Vuestic Admin は Epicmax によって作成され、そしてこれまでのすべての年月を通じてサポートされています。

6年以上にわたる商業およびオープンソースプロジェクトへの専念した作業、および世界中のさまざまな分野で47以上のクライアントを持つことで、Epicmaxは特にVue.jsにおけるフロントエンド開発の深い専門知識を有しています。私たちはプロジェクトのコード監査を定期的に実施しており、これまでのクライアントだけでなく、フロントエンドコードの状態を理解し、セキュアで最新であることを確認したいすべての方にこのサービスを提供することに興奮しています！

Epicmaxによるウェブ開発サービスの相談や注文は、この[フォーム](https://epicmax.co/contacts)からリクエストできます 😎

こんにちはと言いたい方は: [<EMAIL>](mailto:<EMAIL>)。一緒に仕事ができることを嬉しく思います！

[これまでの仕事](https://epicmax.co) 🤘

[チームに会う](https://ui.vuestic.dev/introduction/team)

### 受賞歴

[<img src="https://i.imgur.com/ZeQPZ3Q.png" align="center" width="150px"/>](https://flatlogic.com/templates/vuestic-vue-free-admin)

<p>
  [Flatlogic](https://flatlogic.com/templates/vuestic-vue-free-admin) マーケットプレイスによる
</p>

### 私たちをフォローしてください

最新のVuesticニュースをお知らせします！
[Twitter](https://twitter.com/vuestic_ui) または [Linkedin](https://www.linkedin.com/company/18509340) でフォローしてください。

### ライセンス

[MIT](https://github.com/epicmaxco/vuestic-admin/blob/master/LICENSE) ライセンス。
